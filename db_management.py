#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理工具：PostgreSQL数据库初始化、备份和恢复
"""

import os
import sys
import subprocess
import logging
from datetime import datetime
from sqlalchemy import create_engine, text
from app.routes.db.models import Base

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PostgreSQLManager:
    """PostgreSQL数据库管理器"""
    
    def __init__(self, database_uri):
        """
        初始化管理器
        
        Args:
            database_uri: PostgreSQL数据库连接字符串
        """
        self.database_uri = database_uri
        self.engine = None
        
        # 解析连接信息用于pg_dump/pg_restore
        self.parse_connection_info()
    
    def parse_connection_info(self):
        """解析数据库连接信息"""
        # 例如: postgresql://flask_user:flask_password@localhost:5432/flask_db
        try:
            from urllib.parse import urlparse
            parsed = urlparse(self.database_uri)
            
            self.host = parsed.hostname or 'localhost'
            self.port = parsed.port or 5432
            self.username = parsed.username
            self.password = parsed.password
            self.database = parsed.path.lstrip('/')
            
            logger.info(f"数据库连接信息: {self.username}@{self.host}:{self.port}/{self.database}")
            
        except Exception as e:
            logger.error(f"解析数据库连接信息失败: {e}")
            raise
    
    def connect(self):
        """连接数据库"""
        try:
            logger.info("连接PostgreSQL数据库...")
            self.engine = create_engine(self.database_uri)
            logger.info("数据库连接成功")
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def init_database(self):
        """初始化数据库：创建表结构和索引"""
        try:
            logger.info("初始化数据库...")
            
            if not self.engine:
                self.connect()
            
            # 创建表结构
            logger.info("创建表结构...")
            Base.metadata.create_all(self.engine)
            
            # 创建性能优化索引
            try:
                logger.info("创建性能优化索引...")
                from app.routes.db.migrations.add_performance_indexes import create_indexes
                create_indexes(self.engine)
                logger.info("索引创建成功")
            except Exception as e:
                logger.warning(f"索引创建失败，跳过: {e}")
            
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def backup_database(self, backup_path=None):
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径，如果为None则自动生成
            
        Returns:
            str: 备份文件路径
        """
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_{self.database}_{timestamp}.sql"
            
            logger.info(f"开始备份数据库到: {backup_path}")
            
            # 构建pg_dump命令
            env = os.environ.copy()
            env['PGPASSWORD'] = self.password
            
            cmd = [
                'pg_dump',
                '-h', self.host,
                '-p', str(self.port),
                '-U', self.username,
                '-d', self.database,
                '-f', backup_path,
                '--verbose',
                '--no-owner',
                '--no-privileges'
            ]
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"数据库备份成功: {backup_path}")
                return backup_path
            else:
                logger.error(f"数据库备份失败: {result.stderr}")
                raise Exception(f"pg_dump failed: {result.stderr}")
                
        except Exception as e:
            logger.error(f"备份数据库失败: {e}")
            raise
    
    def restore_database(self, backup_path):
        """
        恢复数据库
        
        Args:
            backup_path: 备份文件路径
        """
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            logger.info(f"开始从备份文件恢复数据库: {backup_path}")
            
            # 首先清空数据库
            if not self.engine:
                self.connect()
            
            logger.info("清空现有数据...")
            Base.metadata.drop_all(self.engine)
            
            # 构建psql命令恢复数据
            env = os.environ.copy()
            env['PGPASSWORD'] = self.password
            
            cmd = [
                'psql',
                '-h', self.host,
                '-p', str(self.port),
                '-U', self.username,
                '-d', self.database,
                '-f', backup_path,
                '--quiet'
            ]
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("数据库恢复成功")
            else:
                logger.error(f"数据库恢复失败: {result.stderr}")
                raise Exception(f"psql failed: {result.stderr}")
                
        except Exception as e:
            logger.error(f"恢复数据库失败: {e}")
            raise
    
    def check_database_status(self):
        """检查数据库状态"""
        try:
            if not self.engine:
                self.connect()
            
            logger.info("检查数据库状态...")
            
            # 检查表是否存在
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """))
                tables = [row[0] for row in result]
            
            logger.info(f"数据库中的表: {tables}")
            
            # 检查每个表的记录数
            table_models = [
                ('orders', 'Order'),
                ('payment_schedules', 'PaymentSchedule'),
                ('transactions', 'Transaction'),
                ('customer_info', 'CustomerInfo')
            ]
            
            for table_name, model_name in table_models:
                if table_name in tables:
                    with self.engine.connect() as conn:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = result.scalar()
                        logger.info(f"表 {table_name}: {count} 条记录")
                else:
                    logger.warning(f"表 {table_name} 不存在")
            
            logger.info("数据库状态检查完成")
            
        except Exception as e:
            logger.error(f"检查数据库状态失败: {e}")
            raise
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python db_management.py init [DATABASE_URI]           # 初始化数据库")
        print("  python db_management.py backup [DATABASE_URI]         # 备份数据库")
        print("  python db_management.py restore <backup_file> [DATABASE_URI]  # 恢复数据库")
        print("  python db_management.py status [DATABASE_URI]         # 检查数据库状态")
        print("")
        print("DATABASE_URI默认值: postgresql://flask_user:flask_password@localhost:5432/flask_db")
        return
    
    command = sys.argv[1]
    
    # 获取数据库URI
    default_uri = 'postgresql://flask_user:flask_password@localhost:5432/flask_db'
    database_uri = os.getenv('DATABASE_URI', default_uri)
    
    if command == 'init':
        if len(sys.argv) > 2:
            database_uri = sys.argv[2]
        
        manager = PostgreSQLManager(database_uri)
        try:
            manager.init_database()
            print("数据库初始化成功!")
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            sys.exit(1)
        finally:
            manager.close_connection()
    
    elif command == 'backup':
        if len(sys.argv) > 2:
            database_uri = sys.argv[2]
        
        manager = PostgreSQLManager(database_uri)
        try:
            backup_file = manager.backup_database()
            print(f"数据库备份成功: {backup_file}")
        except Exception as e:
            print(f"数据库备份失败: {e}")
            sys.exit(1)
        finally:
            manager.close_connection()
    
    elif command == 'restore':
        if len(sys.argv) < 3:
            print("错误: 请指定备份文件路径")
            sys.exit(1)
        
        backup_file = sys.argv[2]
        if len(sys.argv) > 3:
            database_uri = sys.argv[3]
        
        manager = PostgreSQLManager(database_uri)
        try:
            manager.restore_database(backup_file)
            print("数据库恢复成功!")
        except Exception as e:
            print(f"数据库恢复失败: {e}")
            sys.exit(1)
        finally:
            manager.close_connection()
    
    elif command == 'status':
        if len(sys.argv) > 2:
            database_uri = sys.argv[2]
        
        manager = PostgreSQLManager(database_uri)
        try:
            manager.check_database_status()
        except Exception as e:
            print(f"检查数据库状态失败: {e}")
            sys.exit(1)
        finally:
            manager.close_connection()
    
    else:
        print(f"未知命令: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()