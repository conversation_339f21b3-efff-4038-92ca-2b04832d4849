# payment_status_updater.py
# 独立的还款状态更新脚本，可通过任务计划程序每天凌晨1点定时执行

import sys
import os
import logging
import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# 导入ETL模块
from etl import update_payment_status_and_receivable, Base, DB_URI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('payment_updater.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def get_db_session():
    """获取数据库会话"""
    try:
        engine = create_engine(DB_URI, echo=False)
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        return Session()
    except Exception as e:
        logger.error(f"创建数据库会话失败: {str(e)}")
        return None

def run_payment_status_update():
    """运行还款状态更新任务"""
    logger.info("==== 开始执行还款状态自动更新任务 ====")
    start_time = datetime.datetime.now()
    logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取数据库会话
    session = get_db_session()
    if not session:
        logger.error("无法获取数据库会话，任务终止")
        return False
    
    try:
        # 调用现有的更新函数，不改变业务逻辑
        update_payment_status_and_receivable(session)
        
        # 计算执行时间
        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()
        logger.info(f"还款状态更新任务成功完成")
        logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"总耗时: {duration:.2f} 秒")
        return True
    except Exception as e:
        logger.error(f"还款状态更新失败: {str(e)}", exc_info=True)
        return False
    finally:
        session.close()
        logger.info("==== 还款状态更新任务结束 ====")

if __name__ == "__main__":
    run_payment_status_update()
