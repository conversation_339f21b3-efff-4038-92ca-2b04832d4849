# app/routes/customer_summary_db.py
# 使用数据库查询重构的客户订单汇总数据功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
import logging
import traceback

bp = Blueprint('customer_summary_db', __name__)

@bp.route('/customer_summary_db', methods=['GET'])
@require_api_key('customer_summary_db')
def customer_summary_db():
    """
    提供客户订单汇总数据（数据库版本）：
    1. 筛选数据库中客户姓名匹配的所有订单；
    2. 汇总订单数量、总融资额、当前待收、已还款等信息；
    3. 补充基本信息（联系电话、客服归属、业务归属）；
    4. 订单详情中增加订单日期，并计算"当前待收期数"。
    """
    logging.info(f"客户汇总请求参数(DB版): {request.args}")
    logging.info(f"请求来源: {request.remote_addr}")
    try:
        customer_query = request.args.get('customer_name') or request.args.get('phone')
        if not customer_query:
            return jsonify({
                'error': '请提供客户姓名或手机号',
                'message': '例如 ?customer_name=张三 或 ?phone=13812345678'
            }), 400

        # 使用数据库查询工具类获取客户汇总数据
        result = OrderQueries.get_customer_summary(customer_query)
        
        # 如果是错误消息，返回相应的响应
        if isinstance(result, dict) and 'message' in result:
            return jsonify(result), 200
            
        return jsonify(result)
    except Exception as e:
        logging.error(f"客户汇总查询异常(DB版): {e}")
        logging.error(traceback.format_exc())
        return jsonify({'error': '服务器内部错误', 'message': str(e)}), 500
