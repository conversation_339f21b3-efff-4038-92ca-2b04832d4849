from flask import Blueprint, request, jsonify, session, redirect, url_for, render_template
from functools import wraps
import time
import logging
from werkzeug.security import check_password_hash, generate_password_hash

# 创建蓝图
bp = Blueprint('auth', __name__, url_prefix='/api')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 用户凭据（固定账号密码）
VALID_USERNAME = "lxw8080"
VALID_PASSWORD = "Lu8025031"
# 密码哈希值（实际应用中应该存储哈希值而非明文）
PASSWORD_HASH = generate_password_hash(VALID_PASSWORD)

# 登录尝试记录
login_attempts = {}
# IP锁定记录
locked_ips = {}
# 最大尝试次数
MAX_ATTEMPTS = 3
# 锁定时间（秒）
LOCK_DURATION = 24 * 60 * 60  # 24小时

# 登录装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            return jsonify({"success": False, "message": "请先登录"}), 401
        return f(*args, **kwargs)
    return decorated_function

# 检查IP是否被锁定
def is_ip_locked(ip):
    if ip in locked_ips:
        lock_time, duration = locked_ips[ip]
        if time.time() - lock_time < duration:
            # 锁定未过期
            return True
        else:
            # 锁定已过期，删除记录
            del locked_ips[ip]
    return False

@bp.route('/login', methods=['POST'])
def login():
    """用户登录API"""
    data = request.json
    username = data.get('username')
    password = data.get('password')
    
    # 获取客户端IP
    client_ip = request.remote_addr
    
    # 检查IP是否被锁定
    if is_ip_locked(client_ip):
        logger.warning(f"锁定的IP尝试登录: {client_ip}")
        return jsonify({
            "success": False, 
            "message": "您的IP已被锁定，请联系管理员解锁", 
            "locked": True
        })
    
    # 检查登录尝试次数
    if client_ip in login_attempts:
        attempts = login_attempts[client_ip]
        if attempts >= MAX_ATTEMPTS:
            # 锁定IP
            locked_ips[client_ip] = (time.time(), LOCK_DURATION)
            logger.warning(f"IP已锁定: {client_ip}，尝试次数过多")
            return jsonify({
                "success": False, 
                "message": "登录尝试次数过多，您的IP已被锁定", 
                "locked": True
            })
    
    # 验证用户名和密码
    if username == VALID_USERNAME and check_password_hash(PASSWORD_HASH, password):
        # 登录成功，重置尝试次数
        if client_ip in login_attempts:
            del login_attempts[client_ip]
        
        # 设置会话
        session['logged_in'] = True
        session['username'] = username
        
        logger.info(f"用户 {username} 登录成功")
        return jsonify({"success": True, "message": "登录成功"})
    else:
        # 登录失败，增加尝试次数
        if client_ip not in login_attempts:
            login_attempts[client_ip] = 1
        else:
            login_attempts[client_ip] += 1
        
        attempts_left = MAX_ATTEMPTS - login_attempts[client_ip]
        
        logger.warning(f"登录失败: {username}, IP: {client_ip}, 剩余尝试次数: {attempts_left}")
        return jsonify({
            "success": False, 
            "message": "用户名或密码错误", 
            "attempts_left": attempts_left
        })

@bp.route('/logout', methods=['POST'])
def logout():
    """用户登出API"""
    session.pop('logged_in', None)
    session.pop('username', None)
    return jsonify({"success": True, "message": "已成功登出"})

@bp.route('/check_lock_status', methods=['GET'])
def check_lock_status():
    """检查IP锁定状态"""
    client_ip = request.remote_addr
    locked = is_ip_locked(client_ip)
    
    return jsonify({"locked": locked})

@bp.route('/login_page')
def login_page():
    """渲染登录页面"""
    # 如果已登录，重定向到上传页面
    if session.get('logged_in'):
        return redirect(url_for('upload_page.index'))
    return render_template('login.html')
