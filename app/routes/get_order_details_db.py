# app/routes/get_order_details_db.py
# 使用数据库查询重构的获取订单详情功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
import logging

bp = Blueprint('get_order_details_db', __name__)

@bp.route('/get_order_details_db', methods=['GET'])
@require_api_key('get_order_details_db')
def get_order_details_db():
    """
    根据订单编号获取订单的详细信息（数据库版本）。
    """
    order_number_query = request.args.get('order_number')
    if not order_number_query:
        logging.warning("未提供订单编号参数。")
        return jsonify({'error': '请提供订单编号参数，如 ?order_number=123456'}), 400

    try:
        # 使用数据库查询工具类获取订单详情
        order_details = OrderQueries.get_order_details(order_number_query.strip())
        
        if 'error' in order_details:
            logging.info(order_details['error'])
            return jsonify({'message': order_details['error'], 'order_details': {}}), 200
            
        # 转换为与原API兼容的格式
        formatted_details = {
            '订单日期': order_details['order_info']['order_date'],
            '订单编号': order_details['order_info']['order_number'],
            '客户姓名': order_details['order_info']['customer_name'] or '',
            '产品': order_details['order_info']['product_type'] or '',
            '期数': str(order_details['order_info']['periods']) if order_details['order_info']['periods'] else '',
            '总待收': "{:.2f}".format(order_details['order_info']['total_receivable'] or 0),
            '当前待收': "{:.2f}".format(order_details['order_info']['current_receivable'] or 0),
            '备注': order_details['order_info']['remarks'] or ''
        }
        
        # 添加客户信息
        if order_details['customer_info']:
            formatted_details.update({
                '手机号码': order_details['customer_info']['phone'] or '',
                '客服': order_details['customer_info']['customer_service'] or '',
                '业务': order_details['customer_info']['business_affiliation'] or '',
                '客户信息备注': order_details['customer_info']['remarks'] or ''
            })
            
        logging.info(f"成功获取订单详情(DB版)，订单编号: {order_number_query}")
        return jsonify({'order_details': formatted_details})
            
    except Exception as e:
        logging.error(f'获取订单详情错误(DB版)：{str(e)}')
        return jsonify({'error': '获取订单详情错误，请联系管理员。'}), 500
