# app/routes/etl_api.py

from flask import Blueprint, request, jsonify, current_app, Response
from app.routes.auth import login_required
import os
import logging
import time
from werkzeug.utils import secure_filename
import sys
import re
import uuid
import json
from threading import Thread
import queue

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入ETL模块
from etl import run_etl, update_payment_status_and_receivable, update_order_status, update_financial_fields, get_session, sync_to_db, DB_URI
from etl import build_import_report
from app.utils.report_store import make_run_id, save_report, load_report, load_latest_report
from datetime import datetime
import json

# 创建蓝图
bp = Blueprint('etl_api', __name__, url_prefix='/api/etl')

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'xls', 'xlsx', 'xlsm'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def read_etl_logs():
    """从ETL日志文件中读取最近的日志记录"""
    # 获取日志文件路径，优先使用环境变量，否则使用默认路径
    log_file = os.getenv('ETL_LOG_FILE', 'logs/etl.log')
    
    # 如果是相对路径，转换为绝对路径
    if not os.path.isabs(log_file):
        log_file = os.path.abspath(log_file)
    
    logger.info(f"尝试读取日志文件: {log_file}")
    
    if not os.path.exists(log_file):
        logger.warning(f"日志文件不存在: {log_file}")
        return []
    
    logs = []
    try:
        # 读取日志文件的最后200行（足够显示最近的导入过程）
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            lines = all_lines[-200:] if len(all_lines) > 200 else all_lines
            logger.info(f"成功读取日志文件，共 {len(lines)} 行")
        
        for line in lines:
            # 尝试多种日志格式解析
            try:
                # 标准格式: 2025-04-19 22:04:03,618 [INFO] 导入摘要: ...
                if '[INFO]' in line or '[WARNING]' in line or '[ERROR]' in line:
                    # 提取时间部分
                    time_part = line.split('[', 1)[0].strip()
                    # 提取日志级别
                    level_part = line.split('[', 1)[1].split(']', 1)[0] if '[' in line else 'INFO'
                    # 提取消息部分
                    message_part = line.split(']', 1)[1].strip() if ']' in line else line
                    
                    # 保留与ETL和状态更新相关的日志
                    if any(keyword in message_part for keyword in 
                          ['导入摘要', '开始更新还款状态', '还款状态和当前待收更新完成', 
                           '已更新所有还款计划', 'ETL 过程', '开始更新', '更新完成',
                           '订单表', '还款状态', '财务字段', '订单状态', '数据库',
                           '批次', '处理订单批次', '订单编号', '交易', '提交成功', 
                           '同步', '还款计划', '跳过', '行订单管理', '行交易',
                           '合同金额校验', '估算来源', '型号匹配']):
                        logs.append({
                            'time': time_part,
                            'level': level_part,
                            'message': message_part
                        })
            except Exception as parse_error:
                logger.warning(f"解析日志行失败: {str(parse_error)}, 行内容: {line}")
                continue
    except Exception as e:
        logger.error(f"读取日志文件错误: {str(e)}")
    
    logger.info(f"成功解析 {len(logs)} 条相关日志记录")
    return logs

@bp.route('/trigger', methods=['POST'])
@login_required
def trigger_etl():
    """触发ETL过程"""
    try:
        # 从请求中获取 Excel 路径，如果没有提供则使用默认路径
        data = request.get_json() or {}
        excel_path = data.get('excel_path', os.getenv('EXCEL_FILE_PATH'))
        
        if not excel_path:
            return jsonify({
                'success': False,
                'message': '未提供 Excel 文件路径，且环境变量中也未设置'
            }), 400
            
        logger.info(f'收到 ETL 请求，使用 Excel 文件: {excel_path}')
        
        # 运行 ETL 过程
        success, message, orders_count, schedules_count, transactions_count, customers_count = run_etl(excel_path=excel_path)
        
        if not success:
            return jsonify({
                'success': False,
                'message': message
            }), 500
        
        # 创建导入摘要
        import_summary = {
            'orders_count': orders_count,
            'schedules_count': schedules_count,
            'transactions_count': transactions_count,
            'customers_count': customers_count
        }

        # 生成导入报告（当前库状态）
        try:
            session = get_session(DB_URI)
            report = build_import_report(session)
        except Exception as e:
            logger.error(f'生成导入报告失败: {e}', exc_info=True)
            report = None
        finally:
            try:
                session.close()
            except Exception:
                pass

        # 保存导入报告
        run_id = make_run_id()
        report_path = None
        if report is not None:
            try:
                report_path = save_report(run_id, report)
            except Exception as e:
                logger.error(f'保存导入报告失败: {e}', exc_info=True)

        # 获取计算日志
        calculation_logs = read_etl_logs()

        return jsonify({
            'success': True,
            'message': 'ETL过程成功完成',
            'import_summary': import_summary,
            'run_id': run_id,
            'import_report': report,
            'report_path': report_path,
            'calculation_logs': calculation_logs
        }), 200
        
    except Exception as e:
        logger.error(f'ETL API 错误: {str(e)}', exc_info=True)
        return jsonify({
            'success': False,
            'message': f'ETL 过程发生错误: {str(e)}'
        }), 500

@bp.route('/upload', methods=['POST'])
@login_required
def upload_excel():
    """处理Excel文件上传"""
    try:
        # 检查请求中是否包含文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '请求中未包含文件'
            }), 400
            
        file = request.files['file']
        
        # 检查文件名是否为空
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '未选择文件'
            }), 400
            
        # 检查文件扩展名
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': '不支持的文件格式，请上传 Excel 文件 (.xls, .xlsx, .xlsm)'
            }), 400
            
        # 创建上传目录（如果不存在）
        upload_dir = os.path.join(os.getcwd(), 'uploads')
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)
            
        # 生成安全的文件名并保存文件
        filename = secure_filename(file.filename)
        # 添加唯一标识符，避免文件名冲突
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)
        
        logger.info(f'Excel 文件已上传: {file_path}')
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 执行ETL过程
            success, message, orders_count, schedules_count, transactions_count, customers_count = run_etl(excel_path=file_path)
            
            if not success:
                return jsonify({
                    'success': False,
                    'message': message
                }), 500
            
            # 创建导入摘要
            import_summary = {
                'orders_count': orders_count,
                'schedules_count': schedules_count,
                'transactions_count': transactions_count,
                'customers_count': customers_count
            }

            # 生成导入报告
            try:
                session = get_session(DB_URI)
                report = build_import_report(session)
            except Exception as e:
                logger.error(f'生成导入报告失败: {e}', exc_info=True)
                report = None
            finally:
                try:
                    session.close()
                except Exception:
                    pass

            # 保存导入报告
            run_id = make_run_id()
            report_path = None
            if report is not None:
                try:
                    report_path = save_report(run_id, report)
                except Exception as e:
                    logger.error(f'保存导入报告失败: {e}', exc_info=True)

            # 获取计算日志
            calculation_logs = read_etl_logs()
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            return jsonify({
                'success': True,
                'message': f'ETL过程成功完成，处理时间: {processing_time:.2f}秒',
                'import_summary': import_summary,
                'run_id': run_id,
                'import_report': report,
                'report_path': report_path,
                'calculation_logs': calculation_logs,
                'filename': filename
            })
            
        except Exception as e:
            logger.error(f'上传 Excel 文件错误: {str(e)}', exc_info=True)
            return jsonify({
                'success': False,
                'message': f'上传 Excel 文件发生错误: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f'上传 Excel 文件 API 错误: {str(e)}', exc_info=True)
        return jsonify({
            'success': False,
            'message': f'上传 Excel 文件 API 发生错误: {str(e)}'
        }), 500

@bp.route('/update-payment-status', methods=['POST'])
@login_required
def update_payment_status_api():
    """仅更新还款状态和当前待收的 API 端点"""
    try:
        logger.info('收到更新还款状态请求')
        
        # 获取数据库会话
        session = get_session(DB_URI)
        
        try:
            # 更新还款状态和当前待收
            update_payment_status_and_receivable(session)
            
            return jsonify({
                'success': True,
                'message': '还款状态和当前待收更新成功'
            }), 200
            
        except Exception as e:
            logger.error(f'更新还款状态错误: {str(e)}', exc_info=True)
            return jsonify({
                'success': False,
                'message': f'更新还款状态发生错误: {str(e)}'
            }), 500
        finally:
            session.close()
            
    except Exception as e:
        logger.error(f'更新还款状态 API 错误: {str(e)}', exc_info=True)
        return jsonify({
            'success': False,
            'message': f'更新还款状态 API 发生错误: {str(e)}'
        }), 500


@bp.route('/update-status', methods=['POST'])
@login_required
def update_status_api():
    """手动更新账单状态、订单状态和财务字段的 API 端点"""
    try:
        logger.info('收到手动更新状态请求')
        
        # 获取数据库会话
        session = get_session(DB_URI)
        
        try:
            # 更新还款状态和当前待收
            logger.info('开始更新还款状态和当前待收...')
            update_payment_status_and_receivable(session)
            
            # 更新订单状态
            logger.info('开始更新订单状态...')
            update_order_status(session)
            
            # 更新财务字段
            logger.info('开始更新财务字段...')
            update_financial_fields(session)
            
            logger.info('所有状态更新完成')
            
            return jsonify({
                'success': True,
                'message': '账单状态、订单状态和财务字段更新成功'
            }), 200
            
        except Exception as e:
            logger.error(f'更新状态错误: {str(e)}', exc_info=True)
            return jsonify({
                'success': False,
                'message': f'更新状态发生错误: {str(e)}'
            }), 500
        finally:
            session.close()
            
    except Exception as e:
        logger.error(f'更新状态 API 错误: {str(e)}', exc_info=True)
        return jsonify({
            'success': False,
            'message': f'更新状态 API 发生错误: {str(e)}'
        }), 500


@bp.route('/logs', methods=['GET'])
@login_required
def get_logs_api():
    """获取ETL日志的API接口"""
    try:
        # 获取查询参数
        lines = request.args.get('lines', 50, type=int)
        lines = min(lines, 200)  # 限制最大行数
        
        logs = read_etl_logs()
        
        # 只返回最近的指定行数
        recent_logs = logs[-lines:] if len(logs) > lines else logs
        
        return jsonify({
            'success': True,
            'logs': recent_logs,
            'total': len(logs)
        }), 200
        
    except Exception as e:
        logger.error(f'获取日志 API 错误: {str(e)}', exc_info=True)
        return jsonify({
            'success': False,
            'message': f'获取日志发生错误: {str(e)}',
            'logs': []
        }), 500


# 全局变量用于存储日志监控状态
log_watchers = {}


def watch_log_file(client_id, log_queue):
    """监控日志文件变化并推送到队列"""
    log_file = os.getenv('ETL_LOG_FILE', 'logs/etl.log')
    if not os.path.isabs(log_file):
        log_file = os.path.abspath(log_file)
    
    if not os.path.exists(log_file):
        return
    
    # 获取文件初始大小
    last_size = os.path.getsize(log_file)
    
    try:
        while client_id in log_watchers:
            try:
                current_size = os.path.getsize(log_file)
                if current_size > last_size:
                    # 文件有新内容
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_size)
                        new_lines = f.readlines()
                        
                    for line in new_lines:
                        if '[INFO]' in line or '[WARNING]' in line or '[ERROR]' in line:
                            try:
                                # 解析日志行
                                time_part = line.split('[', 1)[0].strip()
                                level_part = line.split('[', 1)[1].split(']', 1)[0] if '[' in line else 'INFO'
                                message_part = line.split(']', 1)[1].strip() if ']' in line else line
                                
                                # 检查是否包含相关关键词
                                if any(keyword in message_part for keyword in 
                                      ['导入摘要', '开始更新还款状态', '还款状态和当前待收更新完成', 
                                       '已更新所有还款计划', 'ETL 过程', '开始更新', '更新完成',
                                       '订单表', '还款状态', '财务字段', '订单状态', '数据库',
                                       '批次', '处理订单批次', '订单编号', '交易', '提交成功', 
                                       '同步', '还款计划', '跳过', '行订单管理', '行交易',
                                       '合同金额校验', '估算来源', '型号匹配']):
                                    
                                    log_entry = {
                                        'time': time_part,
                                        'level': level_part,
                                        'message': message_part
                                    }
                                    
                                    try:
                                        log_queue.put_nowait(log_entry)
                                    except queue.Full:
                                        # 队列满了，移除最老的条目
                                        try:
                                            log_queue.get_nowait()
                                            log_queue.put_nowait(log_entry)
                                        except queue.Empty:
                                            pass
                            except Exception as parse_error:
                                continue
                    
                    last_size = current_size
                
                time.sleep(0.5)  # 每0.5秒检查一次
                
            except Exception as e:
                logger.error(f"监控日志文件错误: {str(e)}")
                time.sleep(1)
                
    except Exception as e:
        logger.error(f"日志监控线程错误: {str(e)}")
    finally:
        # 清理
        if client_id in log_watchers:
            del log_watchers[client_id]


@bp.route('/logs/stream', methods=['GET', 'OPTIONS'])
def stream_logs():
    """SSE实时日志流"""
    logger.info(f"收到SSE请求: {request.method} {request.url}")
    
    # 处理OPTIONS预检请求
    if request.method == 'OPTIONS':
        logger.info("处理OPTIONS预检请求")
        response = Response()
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response.headers['Access-Control-Allow-Credentials'] = 'true'
        return response
    
    # 检查认证
    from flask import session
    if not session.get('logged_in'):
        logger.warning(f"SSE连接被拒绝: 用户未登录")
        return jsonify({'error': '未授权访问'}), 401
    
    client_id = str(uuid.uuid4())
    log_queue = queue.Queue(maxsize=100)
    
    logger.info(f"SSE客户端连接: {client_id}")
    
    # 启动日志监控线程
    log_watchers[client_id] = True
    watcher_thread = Thread(target=watch_log_file, args=(client_id, log_queue))
    watcher_thread.daemon = True
    watcher_thread.start()
    
    def generate():
        try:
            # 发送连接确认
            yield f"data: {{\"type\": \"connected\", \"client_id\": \"{client_id}\"}}\n\n"
            
            # 首先发送最近的日志
            recent_logs = read_etl_logs()
            if recent_logs:
                for log_entry in recent_logs[-20:]:  # 发送最近20条
                    yield f"data: {json.dumps(log_entry, ensure_ascii=False)}\n\n"
            
            # 然后发送实时日志
            while client_id in log_watchers:
                try:
                    log_entry = log_queue.get(timeout=30)  # 30秒超时
                    yield f"data: {json.dumps(log_entry, ensure_ascii=False)}\n\n"
                except queue.Empty:
                    # 发送心跳
                    yield f"data: {{\"type\": \"heartbeat\", \"timestamp\": \"{int(time.time())}\"}}\n\n"
                except Exception as e:
                    logger.error(f"SSE生成错误: {str(e)}")
                    break
                    
        except Exception as e:
            logger.error(f"SSE流错误: {str(e)}")
        finally:
            # 清理
            logger.info(f"SSE客户端断开: {client_id}")
            if client_id in log_watchers:
                del log_watchers[client_id]
    
    response = Response(generate(), mimetype='text/event-stream')
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['Connection'] = 'keep-alive'
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Credentials'] = 'true'
    response.headers['X-Accel-Buffering'] = 'no'  # 禁用nginx缓冲
    return response


@bp.route('/report', methods=['GET'])
@login_required
def get_report():
    """获取导入报告：支持 ?run_id= 或 ?last=true"""
    run_id = request.args.get('run_id')
    last = request.args.get('last', '').lower() in ('1', 'true', 'yes')
    try:
        if run_id:
            report = load_report(run_id)
            if not report:
                return jsonify({'success': False, 'message': f'未找到 run_id={run_id} 的报告'}), 404
            return jsonify({'success': True, 'run_id': run_id, 'report': report})
        if last:
            res = load_latest_report()
            if not res:
                return jsonify({'success': False, 'message': '暂无报告'}), 404
            latest_run_id, report = res
            return jsonify({'success': True, 'run_id': latest_run_id, 'report': report})
        return jsonify({'success': False, 'message': '请提供 run_id 或 last=true'}), 400
    except Exception as e:
        logger.error(f'获取报告失败: {e}', exc_info=True)
        return jsonify({'success': False, 'message': f'获取报告失败: {str(e)}'}), 500


@bp.route('/report/download', methods=['GET'])
@login_required
def download_report():
    """下载导入报告，支持 JSON 或 CSV。

    params:
      - run_id: 指定运行ID；若缺省可与 last=true 搭配
      - last=true: 下载最新报告
      - format=json|csv（默认json）
    """
    run_id = request.args.get('run_id')
    last = request.args.get('last', '').lower() in ('1', 'true', 'yes')
    fmt = (request.args.get('format') or 'json').lower()
    rtype = (request.args.get('type') or '').lower()  # schedules|issues
    try:
        if run_id:
            report = load_report(run_id)
            if not report:
                return jsonify({'success': False, 'message': f'未找到 run_id={run_id} 的报告'}), 404
        elif last:
            res = load_latest_report()
            if not res:
                return jsonify({'success': False, 'message': '暂无报告'}), 404
            run_id, report = res
        else:
            return jsonify({'success': False, 'message': '请提供 run_id 或 last=true'}), 400

        if fmt == 'json':
            resp = jsonify(report)
            resp.headers['Content-Disposition'] = f'attachment; filename=import_report_{run_id}.json'
            return resp

        if fmt == 'csv':
            # CSV: schedules (default) or issues
            import csv
            from io import StringIO
            output = StringIO()
            writer = csv.writer(output)
            if rtype == 'issues':
                writer.writerow(['order_number', 'type', 'severity', 'message'])
                for it in report.get('issues', []):
                    writer.writerow([
                        it.get('order_number'), it.get('type'), it.get('severity'), it.get('message')
                    ])
            else:
                writer.writerow(['order_number', 'product_type', 'period', 'due_date', 'expected_amount', 'paid_amount', 'delta_amount', 'status'])
                for order in report.get('orders', []):
                    for sch in order.get('schedules', []):
                        writer.writerow([
                            order.get('order_number'),
                            order.get('product_type'),
                            sch.get('period'),
                            sch.get('due_date'),
                            sch.get('expected_amount'),
                            sch.get('paid_amount'),
                            sch.get('delta_amount'),
                            sch.get('status'),
                        ])
            csv_data = output.getvalue()
            return Response(csv_data, mimetype='text/csv', headers={
                'Content-Disposition': f'attachment; filename=import_report_{run_id}_{rtype or "schedules"}.csv'
            })

        return jsonify({'success': False, 'message': f'不支持的格式: {fmt}'}), 400
    except Exception as e:
        logger.error(f'下载报告失败: {e}', exc_info=True)
        return jsonify({'success': False, 'message': f'下载报告失败: {str(e)}'}), 500


@bp.route('/metrics', methods=['GET'])
@login_required
def get_metrics():
    """导入与账单相关指标。

    params:
      - delta_threshold: 默认100
      - source: latest|db （默认 latest：从最新报告聚合；db：实时扫描数据库）
    """
    try:
        delta_threshold = float(request.args.get('delta_threshold', 100))
        source = (request.args.get('source') or 'latest').lower()
        run_id = None
        report = None

        if source == 'latest':
            res = load_latest_report()
            if res:
                run_id, report = res
            else:
                # 回退到 DB 构建
                source = 'db'

        metrics = {
            'total_orders': 0,
            'total_schedules': 0,
            'contract_check_pass': 0,
            'contract_check_fail': 0,
            'delta_anomaly_schedules': 0,
            'orders_with_anomaly': 0,
            'overdue_orders': 0,
        }

        if source == 'db':
            # 构建临时报告并聚合
            session = get_session(DB_URI)
            try:
                report = build_import_report(session)
                # 逾期订单数量
                from app.routes.db.models import Order
                metrics['overdue_orders'] = session.query(Order).filter(Order.status == '逾期').count()
            finally:
                session.close()

        if report:
            s = report.get('summary', {})
            metrics['total_orders'] = int(s.get('total_orders') or 0)
            metrics['total_schedules'] = int(s.get('total_schedules') or 0)
            metrics['contract_check_pass'] = int(s.get('contract_check_pass') or 0)
            metrics['contract_check_fail'] = int(s.get('contract_check_fail') or 0)

            # 统计 delta 异常
            orders = report.get('orders', [])
            anomaly_orders = set()
            for o in orders:
                for sch in o.get('schedules', []):
                    try:
                        d = abs(float(sch.get('delta_amount') or 0))
                    except Exception:
                        d = 0.0
                    if d > delta_threshold:
                        metrics['delta_anomaly_schedules'] += 1
                        anomaly_orders.add(o.get('order_number'))
            metrics['orders_with_anomaly'] = len(anomaly_orders)

        return jsonify({
            'success': True,
            'run_id': run_id,
            'generated_at': datetime.utcnow().isoformat() + 'Z',
            'delta_threshold': delta_threshold,
            'metrics': metrics
        })

    except Exception as e:
        logger.error(f'获取指标失败: {e}', exc_info=True)
        return jsonify({'success': False, 'message': f'获取指标失败: {str(e)}'}), 500
