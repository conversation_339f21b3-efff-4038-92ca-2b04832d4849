# app/routes/export_summary.py

from flask import Blueprint, request, jsonify, send_file
from app.auth.decorators import require_api_key
from app.routes.db.models import Order
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import logging
import io
import os
from openpyxl import Workbook

bp = Blueprint('export_summary', __name__)

def get_db_session():
    """获取数据库会话"""
    db_uri = os.getenv('DATABASE_URI', 'sqlite:///data.db')
    engine = create_engine(db_uri)
    Session = sessionmaker(bind=engine)
    return Session()

@bp.route('/export_summary', methods=['GET'])
@require_api_key('export_summary')
def export_summary():
    """
    从数据库导出汇总数据为Excel文件。
    """
    try:
        session = get_db_session()
        
        # 查询所有订单数据
        orders = session.query(Order).all()
        
        # 创建一个新的工作簿用于导出
        export_wb = Workbook()
        export_ws = export_wb.active
        export_ws.title = "汇总数据"

        # 写入表头
        headers = ["订单编号", "客户姓名", "产品", "期数", "总待收", "当前待收", "备注"]
        export_ws.append(headers)

        # 写入数据
        for order in orders:
            row = [
                order.order_number or '',
                order.customer_name or '',
                order.model or '',
                str(order.periods) if order.periods else '',
                "{:.2f}".format(order.total_receivable or 0.0),
                "{:.2f}".format(order.current_receivable or 0.0),
                order.remarks or ''
            ]
            export_ws.append(row)

        # 将工作簿保存到内存中
        output = io.BytesIO()
        export_wb.save(output)
        output.seek(0)

        session.close()
        
        logging.info("成功从数据库导出汇总数据。")
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='summary_data.xlsx'
        )

    except Exception as e:
        logging.error(f'导出汇总数据错误：{str(e)}')
        return jsonify({'error': '导出汇总数据错误，请联系管理员。'}), 500
