import logging
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)


def run_migration(engine):
    """
    Ensure payment_schedules.auto_inferred exists.
    Adds BOOLEAN column `auto_inferred` with default FALSE if missing.
    """
    conn = engine.connect()
    trans = conn.begin()
    try:
        logger.info("检查并添加 payment_schedules.auto_inferred 列（如不存在）")
        conn.execute(text(
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_schema = 'public' AND table_name = 'payment_schedules' AND column_name = 'auto_inferred'
                ) THEN
                    ALTER TABLE payment_schedules ADD COLUMN auto_inferred BOOLEAN NOT NULL DEFAULT FALSE;
                END IF;
            END$$;
            """
        ))

        trans.commit()
        logger.info("payment_schedules.auto_inferred 列迁移完成")
        return True
    except SQLAlchemyError as e:
        logger.error(f"添加 auto_inferred 列失败: {e}")
        trans.rollback()
        return False
    finally:
        conn.close()

