# app/routes/filter_overdue_orders_db.py
# 使用数据库查询重构的逾期订单筛选功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
import logging

bp = Blueprint('filter_overdue_orders_db', __name__)

@bp.route('/filter_overdue_orders_db', methods=['GET'])
@require_api_key('filter_overdue_orders_db')
def filter_overdue_orders_db():
    """
    筛选逾期订单（首次逾期），并返回结果，包含客户信息补充（数据库版本）。
    """
    try:
        # 使用数据库查询工具类筛选逾期订单
        results = OrderQueries.filter_overdue_orders()
        
        if not results:
            logging.info("未找到逾期订单")
            return jsonify({'message': '未找到逾期订单。', 'results': []}), 200
            
        logging.info(f"成功筛选逾期订单(DB版)，找到 {len(results)} 条记录")
        return jsonify({'results': results})
    except Exception as e:
        logging.error(f'数据处理错误(DB版)：{str(e)}')
        logging.exception("详细错误信息")
        return jsonify({'error': '数据处理错误，请联系管理员。'}), 500
