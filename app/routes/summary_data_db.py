# app/routes/summary_data_db.py

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
import logging
from datetime import datetime
import time
import traceback

# 导入重构后的服务层
from app.services.summary_service import SummaryService
from app.routes.db.queries import OrderQueries  # 保留用于向后兼容

# 创建专用的日志记录器
logger = logging.getLogger(__name__)

bp = Blueprint('summary_data_db', __name__)

@bp.route('/summary_data_db', methods=['GET'])
@require_api_key('summary_data')
def summary_data_db():
    """
    根据指定时间段统计店铺的数据汇总，并增加累计数据和逾期金额。
    重构版本：使用分层架构提升性能和可维护性。
    累计统计从数据库中最早的订单日期开始计算，直到用户指定的结束日期。
    
    查询参数:
        start_date: 开始日期 (格式: YYYY-MM-DD)
        end_date: 结束日期 (格式: YYYY-MM-DD)
        
    返回:
        JSON对象，包含表头和汇总数据
    """
    start_time = time.time()
    logger.info("======================== 数据汇总接口请求开始 (重构版本) ========================")
    
    # 参数验证
    start_date_str = request.args.get("start_date")
    end_date_str = request.args.get("end_date")

    if not start_date_str or not end_date_str:
        logger.warning("未提供时间段参数。")
        return jsonify({"error": "请提供时间段参数，如 ?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD"}), 400

    try:
        # 使用新的服务层
        summary_service = SummaryService()
        
        # 验证和解析请求参数
        params_start_time = time.time()
        summary_request, error_msg = summary_service.validate_request(start_date_str, end_date_str)
        
        if error_msg:
            logger.error(f"请求参数验证失败: {error_msg}")
            return jsonify({"error": error_msg}), 400
        
        params_time = time.time() - params_start_time
        logger.info(f"参数验证耗时: {params_time:.4f}秒")
        logger.info(f"数据汇总请求 - 开始日期: {summary_request.start_date}, 结束日期: {summary_request.end_date}")
        
        # 调用服务层获取汇总数据
        service_start_time = time.time()
        response = summary_service.get_summary_data(summary_request)
        service_time = time.time() - service_start_time
        
        # 更新时间统计
        response.timing_stats["参数验证"] = f"{params_time:.4f}秒"
        response.timing_stats["服务层处理"] = f"{service_time:.4f}秒"
        
        # 计算总处理时间
        total_duration = time.time() - start_time
        response.timing_stats["接口总耗时"] = f"{total_duration:.4f}秒"
        
        logger.info(f"数据汇总接口总耗时: {total_duration:.4f}秒")
        logger.info("======================== 数据汇总接口请求完成 (重构版本) ========================")
        
        return jsonify({
            "headers": response.headers,
            "summary": response.summary,
            "timing_stats": response.timing_stats
        })
        
    except Exception as e:
        # 记录异常详细信息
        error_msg = f"获取汇总数据失败: {str(e)}"
        logger.error(error_msg)
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        
        # 计算错误处理总时间
        error_total_time = time.time() - start_time
        logger.info(f"错误处理总耗时: {error_total_time:.4f}秒")
        logger.info("======================== 数据汇总接口请求失败 (重构版本) ========================")
        
        return jsonify({
            "error": error_msg,
            "status": "error",
            "timestamp": datetime.now().isoformat()
        }), 500


@bp.route('/summary_data_db_legacy', methods=['GET'])
@require_api_key('summary_data')
def summary_data_db_legacy():
    """
    向后兼容的原始接口版本
    保留原有的复杂逻辑，用于对比和渐进式迁移
    """
    from app.utils.date_parser import parse_date
    from app.routes.db import get_db_session, close_db_session
    from app.routes.db.models import Order
    from collections import defaultdict
    
    start_time = time.time()
    logger.info("======================== 数据汇总接口请求开始 (原始版本) ========================")
    
    # 参数验证
    start_date_str = request.args.get("start_date")
    end_date_str = request.args.get("end_date")

    if not start_date_str or not end_date_str:
        logger.warning("未提供时间段参数。")
        return jsonify({"error": "请提供时间段参数，如 ?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD"}), 400

    # 使用原有逻辑进行参数解析
    try:
        params_start_time = time.time()
        start_date = parse_date(start_date_str.strip(), "开始日期")
        if start_date is None:
            logger.error(f"开始日期格式错误: {start_date_str}")
            return jsonify({"error": "开始日期格式不正确，请使用 YYYY-MM-DD 格式。"}), 400

        end_date = parse_date(end_date_str.strip(), "结束日期")
        if end_date is None:
            logger.error(f"结束日期格式错误: {end_date_str}")
            return jsonify({"error": "结束日期格式不正确，请使用 YYYY-MM-DD 格式。"}), 400
            
        if start_date > end_date:
            logger.error(f"开始日期晚于结束日期: {start_date} > {end_date}")
            return jsonify({"error": "开始日期不能晚于结束日期。"}), 400
        
        params_time = time.time() - params_start_time
        logger.info(f"参数解析耗时: {params_time:.4f}秒")
    except Exception as e:
        logger.error(f"日期解析错误: {str(e)}")
        return jsonify({"error": f"日期解析错误: {str(e)}"}), 400

    logger.info(f"数据汇总请求 - 开始日期: {start_date}, 结束日期: {end_date}")
    
    # 使用原有的OrderQueries逻辑
    session = None
    try:
        earliest_date_query_start = time.time()
        session = get_db_session()
        try:
            earliest_date = session.query(func.min(Order.order_date)).scalar()
            logger.info(f"数据库中最早的订单日期: {earliest_date}")
            if earliest_date is None:
                earliest_date = start_date
            
            earliest_date_query_time = time.time() - earliest_date_query_start
            logger.info(f"查询最早订单日期耗时: {earliest_date_query_time:.4f}秒")
        finally:
            close_db_session(session)
        
        # 使用原有的查询逻辑
        query_start_time = time.time()
        
        headers, period_summary_data = OrderQueries.get_summary_data(
            start_date, end_date, False
        )
        
        _, cumulative_summary_data = OrderQueries.get_summary_data(
            earliest_date, end_date, True
        )
        
        period_query_time = time.time() - query_start_time
        logger.info(f"汇总查询耗时: {period_query_time:.4f}秒")
        
        # 数据合并逻辑（保持原有逻辑）
        merge_start_time = time.time()
        
        for row in cumulative_summary_data:
            row[0] = f"累计 {row[0]}"
        
        summary_data = period_summary_data + cumulative_summary_data
        merge_time = time.time() - merge_start_time
        logger.info(f"数据合并耗时: {merge_time:.4f}秒")
        
        # 逾期数据处理（保持原有逻辑）
        overdue_start_time = time.time()
        
        session = get_db_session()
        try:
            overdue_orders = session.query(
                Order.id,
                Order.shop_affiliation,
                Order.overdue_principal,
                Order.current_receivable
            ).filter(
                Order.status == '逾期'
            ).all()
            
            shop_overdue_counts = defaultdict(int)
            shop_overdue_principal = defaultdict(float)
            shop_overdue_receivable = defaultdict(float)
            
            for order_id, shop_name, overdue_principal, current_receivable in overdue_orders:
                if shop_name:
                    shop_overdue_counts[shop_name] += 1
                    shop_overdue_principal[shop_name] += (overdue_principal or 0)
                    shop_overdue_receivable[shop_name] += (current_receivable or 0)
            
            total_overdue_count = sum(shop_overdue_counts.values())
            total_overdue_principal = sum(shop_overdue_principal.values())
            total_overdue_receivable = sum(shop_overdue_receivable.values())
            
            shop_overdue_counts["总平台"] = total_overdue_count
            shop_overdue_principal["总平台"] = total_overdue_principal
            shop_overdue_receivable["总平台"] = total_overdue_receivable
            
            for i, row in enumerate(summary_data):
                shop_name = row[0]
                if shop_name.startswith("累计 "):
                    original_shop_name = shop_name.replace("累计 ", "")
                    if original_shop_name in shop_overdue_counts:
                        new_row = row[:]
                        new_row[17] = round(shop_overdue_principal[original_shop_name], 2)
                        new_row[18] = round(shop_overdue_receivable[original_shop_name], 2)
                        new_row[22] = shop_overdue_counts[original_shop_name]
                        summary_data[i] = new_row
                elif shop_name in shop_overdue_counts:
                    new_row = row[:]
                    new_row[17] = round(shop_overdue_principal[shop_name], 2)
                    new_row[18] = round(shop_overdue_receivable[shop_name], 2)
                    new_row[22] = shop_overdue_counts[shop_name]
                    summary_data[i] = new_row
        finally:
            if session:
                session.close()
        
        overdue_time = time.time() - overdue_start_time
        logger.info(f"逾期数据处理耗时: {overdue_time:.4f}秒")
        
        # 去重和排序（保持原有逻辑）
        format_start_time = time.time()
        
        seen_shop_names = set()
        unique_summary_data = []
        
        for row in summary_data:
            shop_name = row[0]
            if shop_name not in seen_shop_names:
                seen_shop_names.add(shop_name)
                unique_summary_data.append(row)
        
        summary_data = unique_summary_data
        
        shop_order = {
            "总平台": 0, "太太租物": 1, "涛涛好物": 2, "刚刚好物": 3, "林林租物": 4, "太太享物": 5,
            "累计 总平台": 6, "累计 太太租物": 7, "累计 涛涛好物": 8, "累计 刚刚好物": 9, "累计 林林租物": 10, "累计 太太享物": 11
        }
        
        summary_data.sort(key=lambda x: shop_order.get(x[0], 999))
        
        format_time = time.time() - format_start_time
        logger.info(f"数据格式化耗时: {format_time:.4f}秒")
        
        total_duration = time.time() - start_time
        logger.info(f"数据汇总接口总耗时: {total_duration:.4f}秒")
        logger.info("======================== 数据汇总接口请求完成 (原始版本) ========================")
        
        timing_stats = {
            "参数解析": f"{params_time:.4f}秒",
            "汇总查询": f"{period_query_time:.4f}秒",
            "数据合并": f"{merge_time:.4f}秒",
            "逾期数据处理": f"{overdue_time:.4f}秒",
            "数据格式化": f"{format_time:.4f}秒",
            "总耗时": f"{total_duration:.4f}秒"
        }
        
        return jsonify({
            "headers": headers,
            "summary": summary_data,
            "timing_stats": timing_stats
        })
        
    except Exception as e:
        error_msg = f"获取汇总数据失败: {str(e)}"
        logger.error(error_msg)
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        
        error_total_time = time.time() - start_time
        logger.info(f"错误处理总耗时: {error_total_time:.4f}秒")
        logger.info("======================== 数据汇总接口请求失败 (原始版本) ========================")
        
        return jsonify({
            "error": error_msg,
            "status": "error",
            "timestamp": datetime.now().isoformat()
        }), 500
