# app/routes/overdue_summary.py

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.utils.date_parser import parse_date
import logging
from app.config import Config
from datetime import datetime
import calendar
from collections import defaultdict
from sqlalchemy import and_, func
from app.routes.db import get_db_session, close_db_session
from app.routes.db.models import Order, PaymentSchedule
from app.routes.db.queries import OrderQueries  # 添加对OrderQueries的引用

bp = Blueprint('overdue_summary', __name__)

# Excel版本的逾期汇总功能已移除，因为缺少excel_handler模块
# 如需要Excel版本，请先实现相关的Excel处理模块

@bp.route('/overdue_summary_db', methods=['GET'])
@require_api_key('overdue_summary')
def overdue_summary_db():
    """
    基于数据库的逾期订单汇总API
    返回从数据库中最早的逾期订单日期到指定结束日期的逾期订单数据汇总
    """
    end_date_str = request.args.get('end_date')
    if not end_date_str:
        logging.warning("未提供结束日期参数。")
        return jsonify({'error': '请提供结束日期参数，如 ?end_date=YYYY-MM-DD'}), 400

    end_date = parse_date(end_date_str.strip(), "结束日期")
    if end_date is None:
        return jsonify({'error': '日期格式不正确，请使用 YYYY-MM-DD 格式。'}), 400

    try:
        # 使用数据库查询工具类获取逾期订单汇总数据
        summary_data = OrderQueries.get_overdue_summary(end_date)
        return jsonify(summary_data)
    except Exception as e:
        logging.error(f"处理逾期订单数据时出错(DB版): {str(e)}")
        return jsonify({'error': f'数据处理错误: {str(e)}'}), 500