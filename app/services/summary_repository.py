# app/services/summary_repository.py
"""
汇总数据访问层
负责高效的数据库查询，使用聚合查询优化性能
"""

import logging
from datetime import date
from typing import List, Tuple, Optional, Dict
from decimal import Decimal
from sqlalchemy import func, case, and_, or_, text
from sqlalchemy.orm import Session
from collections import defaultdict

from app.routes.db import get_db_session, close_db_session
from app.routes.db.models import Order, Transaction
from app.services.dto import OrderAggregateData, TransactionAggregateData, OverdueData
from app.services.config import SummaryConfig

logger = logging.getLogger(__name__)


class SummaryRepository:
    """汇总数据仓库类"""
    
    def get_earliest_order_date(self) -> Optional[date]:
        """获取数据库中最早的订单日期"""
        session = get_db_session()
        try:
            earliest_date = session.query(func.min(Order.order_date)).scalar()
            logger.info(f"数据库中最早的订单日期: {earliest_date}")
            return earliest_date
        finally:
            close_db_session(session)
    
    def get_aggregated_order_data(self, start_date: date, end_date: date) -> List[OrderAggregateData]:
        """
        获取聚合的订单数据
        使用单次SQL查询获取所有需要的订单统计数据
        注意：订单成本使用“放款”交易金额，而不Order.cost字段
        """
        session = get_db_session()
        try:
            # 使用聚合查询一次性获取所有店铺的订单数据
            # 订单成本使用“放款”交易金额的聚合
            loan_subquery = session.query(
                Transaction.order_id,
                func.sum(func.coalesce(Transaction.amount, 0)).label('loan_cost')
            ).filter(
                Transaction.transaction_date.between(start_date, end_date),
                Transaction.transaction_type == '放款'
            ).group_by(Transaction.order_id).subquery()
            
            query = session.query(
                Order.shop_affiliation,
                Order.product_type,
                Order.status,
                func.sum(func.coalesce(Order.devices_count, 1)).label('total_devices'),
                func.sum(func.coalesce(Order.total_receivable, 0)).label('total_receivable'),
                func.sum(func.coalesce(Order.current_receivable, 0)).label('current_receivable'),
                func.sum(func.coalesce(Order.overdue_principal, 0)).label('overdue_principal'),
                func.sum(func.coalesce(loan_subquery.c.loan_cost, 0)).label('cost')
            ).outerjoin(
                loan_subquery, Order.id == loan_subquery.c.order_id
            ).filter(
                Order.order_date.between(start_date, end_date),
                Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES)
            ).group_by(
                Order.shop_affiliation,
                Order.product_type,
                Order.status
            )
            
            results = query.all()
            
            return [
                OrderAggregateData(
                    shop_affiliation=row.shop_affiliation,
                    product_type=row.product_type or "",
                    status=row.status or "",
                    total_devices=int(row.total_devices or 0),
                    total_receivable=Decimal(str(row.total_receivable or 0)),
                    current_receivable=Decimal(str(row.current_receivable or 0)),
                    overdue_principal=Decimal(str(row.overdue_principal or 0)) if row.overdue_principal else None,
                    cost=Decimal(str(row.cost or 0)) if row.cost else None
                )
                for row in results
            ]
        finally:
            close_db_session(session)

    def get_orders_basic_for_period(self, start_date: date, end_date: date):
        """
        获取周期内按订单级别的数据（用于精确周期待收调整）。
        返回字段：order_id, shop_affiliation, product_type, status, total_receivable
        仅返回在途/逾期等活跃状态订单。
        """
        session = get_db_session()
        try:
            results = session.query(
                Order.id.label('order_id'),
                Order.shop_affiliation,
                Order.product_type,
                Order.status,
                func.coalesce(Order.total_receivable, 0).label('total_receivable')
            ).filter(
                Order.order_date.between(start_date, end_date),
                Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES),
                Order.status.in_(SummaryConfig.ACTIVE_ORDER_STATUSES)
            ).all()
            return results
        finally:
            close_db_session(session)
    
    def get_aggregated_transaction_data(self, start_date: date, end_date: date) -> List[TransactionAggregateData]:
        """
        获取聚合的交易数据
        包含订单的产品类型信息，用于分类统计
        """
        session = get_db_session()
        try:
            # 关联查询交易和订单数据，按店铺和交易类型聚合
            query = session.query(
                Order.shop_affiliation,
                Transaction.transaction_type,
                Order.product_type,
                func.sum(func.coalesce(Transaction.amount, 0)).label('total_amount')
            ).join(
                Order, Transaction.order_id == Order.id
            ).filter(
                Transaction.transaction_date.between(start_date, end_date),
                Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES)
            ).group_by(
                Order.shop_affiliation,
                Transaction.transaction_type,
                Order.product_type
            )
            
            results = query.all()
            
            return [
                TransactionAggregateData(
                    shop_affiliation=row.shop_affiliation,
                    transaction_type=row.transaction_type or "",
                    order_product_type=row.product_type,
                    total_amount=Decimal(str(row.total_amount or 0))
                )
                for row in results
            ]
        finally:
            close_db_session(session)
    
    def get_unassigned_cost_transactions(self, start_date: date, end_date: date) -> Decimal:
        """
        获取未分配到店铺的成本总额
        注意：仅包括交易表中的成本相关交易，不包括订单表的cost字段
        """
        session = get_db_session()
        try:
            # 仅查询未关联订单或未归属到指定店铺的成本交易
            cost_keywords = SummaryConfig.TRANSACTION_TYPE_KEYWORDS["成本"]
            
            transaction_cost = session.query(
                func.sum(func.coalesce(Transaction.amount, 0))
            ).outerjoin(
                Order, Transaction.order_id == Order.id
            ).filter(
                Transaction.transaction_date.between(start_date, end_date),
                Transaction.transaction_type.in_(cost_keywords),
                or_(
                    Transaction.order_id.is_(None),
                    Order.shop_affiliation.is_(None),
                    ~Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES)
                )
            ).scalar()
            
            # 仅使用交易成本，不再使用订单表的cost字段
            transaction_cost_val = Decimal(str(transaction_cost or 0))
            
            logger.info(f"未分配成本统计 - 交易成本: {transaction_cost_val}")
            
            return transaction_cost_val
        finally:
            close_db_session(session)
    
    def get_overdue_data(self) -> List[OverdueData]:
        """
        获取逾期订单数据
        注意：逾期数据不受时间段限制
        """
        session = get_db_session()
        try:
            # 查询所有逾期订单的聚合数据
            query = session.query(
                Order.shop_affiliation,
                func.count().label('overdue_count'),
                func.sum(func.coalesce(Order.overdue_principal, 0)).label('overdue_principal'),
                func.sum(func.coalesce(Order.current_receivable, 0)).label('overdue_receivable')
            ).filter(
                Order.status == SummaryConfig.OVERDUE_ORDER_STATUS,
                Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES + ["总平台"])
            ).group_by(
                Order.shop_affiliation
            )
            
            results = query.all()
            
            overdue_data = [
                OverdueData(
                    shop_affiliation=row.shop_affiliation,
                    overdue_count=int(row.overdue_count or 0),
                    overdue_principal=Decimal(str(row.overdue_principal or 0)),
                    overdue_receivable=Decimal(str(row.overdue_receivable or 0))
                )
                for row in results
            ]
            
            # 计算总平台数据
            if overdue_data:
                total_count = sum(data.overdue_count for data in overdue_data)
                total_principal = sum(data.overdue_principal for data in overdue_data)
                total_receivable = sum(data.overdue_receivable for data in overdue_data)
                
                # 添加总平台数据
                overdue_data.append(OverdueData(
                    shop_affiliation="总平台",
                    overdue_count=total_count,
                    overdue_principal=total_principal,
                    overdue_receivable=total_receivable
                ))
            
            return overdue_data
        finally:
            close_db_session(session)
    
    def get_period_repayment_data(self, start_date: date, end_date: date) -> Dict[int, Decimal]:
        """
        获取时间段内的还款数据，按订单ID分组
        用于周期查询的特殊计算逻辑
        """
        session = get_db_session()
        try:
            # 查询时间段内的还款交易
            repayment_keywords = ["首付", "首付款", "租金", "尾款"]
            
            query = session.query(
                Transaction.order_id,
                func.sum(func.coalesce(Transaction.amount, 0)).label('repayment_amount')
            ).filter(
                Transaction.transaction_date.between(start_date, end_date),
                or_(*[Transaction.transaction_type.like(f"%{keyword}%") for keyword in repayment_keywords])
            ).group_by(
                Transaction.order_id
            )
            
            results = query.all()
            return {row.order_id: Decimal(str(row.repayment_amount or 0)) for row in results}
        finally:
            close_db_session(session)
    
    def get_cumulative_order_data(self, end_date: date) -> List[OrderAggregateData]:
        """
        获取累计订单数据（截至指定日期）
        用于累计查询的计算
        """
        session = get_db_session()
        try:
            # 成本口径统一：按“放款”交易聚合作为成本来源（累计：<= end_date）
            loan_subquery = session.query(
                Transaction.order_id,
                func.sum(func.coalesce(Transaction.amount, 0)).label('loan_cost')
            ).filter(
                Transaction.transaction_date <= end_date,
                Transaction.transaction_type == '放款'
            ).group_by(Transaction.order_id).subquery()

            # 查询截至指定日期的所有订单数据（累计）
            query = session.query(
                Order.shop_affiliation,
                Order.product_type,
                Order.status,
                func.sum(func.coalesce(Order.devices_count, 1)).label('total_devices'),
                func.sum(func.coalesce(Order.total_receivable, 0)).label('total_receivable'),
                func.sum(func.coalesce(Order.current_receivable, 0)).label('current_receivable'),
                func.sum(func.coalesce(Order.overdue_principal, 0)).label('overdue_principal'),
                func.sum(func.coalesce(loan_subquery.c.loan_cost, 0)).label('cost')
            ).outerjoin(
                loan_subquery, Order.id == loan_subquery.c.order_id
            ).filter(
                Order.order_date <= end_date,
                Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES)
            ).group_by(
                Order.shop_affiliation,
                Order.product_type,
                Order.status
            )
            
            results = query.all()
            
            return [
                OrderAggregateData(
                    shop_affiliation=row.shop_affiliation,
                    product_type=row.product_type or "",
                    status=row.status or "",
                    total_devices=int(row.total_devices or 0),
                    total_receivable=Decimal(str(row.total_receivable or 0)),
                    current_receivable=Decimal(str(row.current_receivable or 0)),
                    overdue_principal=Decimal(str(row.overdue_principal or 0)) if row.overdue_principal else None,
                    cost=Decimal(str(row.cost or 0)) if row.cost else None
                )
                for row in results
            ]
        finally:
            close_db_session(session)
    
    def get_cumulative_transaction_data(self, end_date: date) -> List[TransactionAggregateData]:
        """
        获取累计交易数据（截至指定日期）
        """
        session = get_db_session()
        try:
            query = session.query(
                Order.shop_affiliation,
                Transaction.transaction_type,
                Order.product_type,
                func.sum(func.coalesce(Transaction.amount, 0)).label('total_amount')
            ).join(
                Order, Transaction.order_id == Order.id
            ).filter(
                Transaction.transaction_date <= end_date,
                Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES)
            ).group_by(
                Order.shop_affiliation,
                Transaction.transaction_type,
                Order.product_type
            )
            
            results = query.all()
            
            return [
                TransactionAggregateData(
                    shop_affiliation=row.shop_affiliation,
                    transaction_type=row.transaction_type or "",
                    order_product_type=row.product_type,
                    total_amount=Decimal(str(row.total_amount or 0))
                )
                for row in results
            ]
        finally:
            close_db_session(session)
    
    def get_cumulative_unassigned_cost(self, end_date: date) -> Decimal:
        """
        获取累计的未分配成本
        注意：仅包括交易表中的成本相关交易，不包括订单表的cost字段
        """
        session = get_db_session()
        try:
            # 仅查询截至指定日期的未分配成本交易
            cost_keywords = SummaryConfig.TRANSACTION_TYPE_KEYWORDS["成本"]
            
            transaction_cost = session.query(
                func.sum(func.coalesce(Transaction.amount, 0))
            ).outerjoin(
                Order, Transaction.order_id == Order.id
            ).filter(
                Transaction.transaction_date <= end_date,
                Transaction.transaction_type.in_(cost_keywords),
                or_(
                    Transaction.order_id.is_(None),
                    Order.shop_affiliation.is_(None),
                    ~Order.shop_affiliation.in_(SummaryConfig.SHOP_NAMES)
                )
            ).scalar()
            
            # 仅使用交易成本，不再使用订单表的cost字段
            transaction_cost_val = Decimal(str(transaction_cost or 0))
            
            logger.info(f"累计未分配成本统计 - 交易成本: {transaction_cost_val}")
            
            return transaction_cost_val
        finally:
            close_db_session(session)
    
    def get_total_business_cost(self, end_date: date) -> Decimal:
        """
        获取所有真实业务成本（包括有归属和无归属的）
        仅统计交易表中成本相关关键词的交易
        """
        session = get_db_session()
        try:
            # 查询所有成本相关交易（不管是否有归属）
            cost_keywords = SummaryConfig.TRANSACTION_TYPE_KEYWORDS["成本"]
            
            total_cost = session.query(
                func.sum(func.coalesce(Transaction.amount, 0))
            ).filter(
                Transaction.transaction_date <= end_date,
                Transaction.transaction_type.in_(cost_keywords)
            ).scalar()
            
            total_cost_val = Decimal(str(total_cost or 0))
            
            logger.info(f"总业务成本统计 - 所有成本交易: {total_cost_val}")
            
            return total_cost_val
        finally:
            close_db_session(session)
