# app/services/dto.py
"""
数据传输对象 (Data Transfer Objects)
定义各种数据结构，用于在不同层之间传递数据
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from datetime import date
from decimal import Decimal


@dataclass
class ShopSummaryData:
    """店铺汇总数据"""
    shop_name: str
    total_devices: int = 0                    # 总台数
    total_receivable: Decimal = Decimal('0')  # 总待收
    lease_receivable: Decimal = Decimal('0')  # 租赁待收
    ecommerce_receivable: Decimal = Decimal('0')  # 电商待收
    value_added_fee: Decimal = Decimal('0')   # 增值费
    extended_warranty: Decimal = Decimal('0') # 延保服务
    down_payment: Decimal = Decimal('0')      # 首付款
    rent: Decimal = Decimal('0')              # 租金
    final_payment: Decimal = Decimal('0')     # 尾款
    loan_amount: Decimal = Decimal('0')       # 放款
    reinvestment: Decimal = Decimal('0')      # 复投
    supplier_profit: Decimal = Decimal('0')   # 供应商利润
    cost: Decimal = Decimal('0')              # 成本
    ecommerce_performance: Decimal = Decimal('0')  # 电商业绩
    lease_performance: Decimal = Decimal('0')      # 租赁业绩
    actual_investment: Decimal = Decimal('0')      # 实际出资
    overdue_principal: Decimal = Decimal('0')      # 逾期本金
    overdue_receivable: Decimal = Decimal('0')     # 逾期总待收
    completed_orders: int = 0                      # 已完成订单
    ecommerce_orders: int = 0                      # 电商订单数
    lease_orders: int = 0                          # 租赁订单数
    overdue_orders: int = 0                        # 逾期订单数

    def to_list(self) -> List:
        """转换为列表格式，用于API响应"""
        return [
            self.shop_name,
            self.total_devices,
            round(float(self.total_receivable), 4),
            round(float(self.lease_receivable), 4),
            round(float(self.ecommerce_receivable), 4),
            round(float(self.value_added_fee), 4),
            round(float(self.extended_warranty), 4),
            round(float(self.down_payment), 4),
            round(float(self.rent), 4),
            round(float(self.final_payment), 4),
            round(float(self.loan_amount), 4),
            round(float(self.reinvestment), 4),
            round(float(self.supplier_profit), 4),
            round(float(abs(self.cost)), 4),  # 成本显示为绝对值
            round(float(self.ecommerce_performance), 4),
            round(float(self.lease_performance), 4),
            round(float(self.actual_investment), 4),
            round(float(self.overdue_principal), 4),
            round(float(self.overdue_receivable), 4),
            self.completed_orders,
            self.ecommerce_orders,
            self.lease_orders,
            self.overdue_orders,
        ]


@dataclass
class OrderAggregateData:
    """订单聚合数据"""
    shop_affiliation: str
    product_type: str
    status: str
    total_devices: int
    total_receivable: Decimal
    current_receivable: Decimal
    overdue_principal: Optional[Decimal]
    cost: Optional[Decimal]


@dataclass
class TransactionAggregateData:
    """交易聚合数据"""
    shop_affiliation: str
    transaction_type: str
    order_product_type: Optional[str]
    total_amount: Decimal


@dataclass
class OverdueData:
    """逾期数据"""
    shop_affiliation: str
    overdue_count: int
    overdue_principal: Decimal
    overdue_receivable: Decimal


@dataclass
class SummaryRequest:
    """汇总请求参数"""
    start_date: date
    end_date: date
    
    def validate(self) -> Optional[str]:
        """验证请求参数"""
        if self.start_date > self.end_date:
            return "开始日期不能晚于结束日期"
        return None


@dataclass
class SummaryResponse:
    """汇总响应数据"""
    headers: List[str]
    summary: List[List]
    timing_stats: Dict[str, str]