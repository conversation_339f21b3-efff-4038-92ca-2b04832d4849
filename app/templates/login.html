<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据导入系统 - 登录</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #333;
        }
        .login-container {
            width: 360px;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e88e5;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn-login {
            width: 100%;
            padding: 12px;
            background-color: #1e88e5;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-login:hover {
            background-color: #1976d2;
        }
        .error-message {
            color: #f44336;
            margin-top: 20px;
            text-align: center;
            display: none;
        }
        .attempts-left {
            color: #ff9800;
            margin-top: 10px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>Excel数据导入系统</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn-login">登录</button>
        </form>
        <div id="errorMessage" class="error-message"></div>
        <div id="attemptsLeft" class="attempts-left"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已被锁定
            checkLockStatus();
            
            const loginForm = document.getElementById('loginForm');
            const errorMessage = document.getElementById('errorMessage');
            const attemptsLeft = document.getElementById('attemptsLeft');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // 发送登录请求
                fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 登录成功，重定向到上传页面
                        window.location.href = '/upload';
                    } else {
                        // 登录失败，显示错误信息
                        errorMessage.textContent = data.message;
                        errorMessage.style.display = 'block';
                        
                        // 显示剩余尝试次数
                        if (data.attempts_left !== undefined) {
                            attemptsLeft.textContent = `剩余尝试次数: ${data.attempts_left}`;
                            attemptsLeft.style.display = 'block';
                        }
                        
                        // 如果被锁定，禁用表单
                        if (data.locked) {
                            loginForm.querySelectorAll('input, button').forEach(el => {
                                el.disabled = true;
                            });
                            localStorage.setItem('login_locked', 'true');
                        }
                    }
                })
                .catch(error => {
                    console.error('登录请求失败:', error);
                    errorMessage.textContent = '登录请求失败，请稍后再试';
                    errorMessage.style.display = 'block';
                });
            });
            
            // 检查是否被锁定
            function checkLockStatus() {
                if (localStorage.getItem('login_locked') === 'true') {
                    // 发送请求检查锁定状态是否已解除
                    fetch('/api/check_lock_status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.locked) {
                            // 仍然被锁定
                            errorMessage.textContent = '您的IP已被锁定，请联系管理员解锁';
                            errorMessage.style.display = 'block';
                            
                            loginForm.querySelectorAll('input, button').forEach(el => {
                                el.disabled = true;
                            });
                        } else {
                            // 锁定已解除
                            localStorage.removeItem('login_locked');
                        }
                    })
                    .catch(error => {
                        console.error('检查锁定状态失败:', error);
                    });
                }
            }
        });
    </script>
</body>
</html>
