<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据导入系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e88e5;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 5px;
            text-align: center;
        }
        .upload-section.drag-over {
            border-color: #1e88e5;
            background-color: rgba(30, 136, 229, 0.05);
        }
        .btn {
            background-color: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #1565c0;
        }
        .btn:disabled {
            background-color: #b0bec5;
            cursor: not-allowed;
        }
        .file-info {
            margin-top: 15px;
            font-size: 14px;
        }
        .progress-container {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            margin-top: 20px;
            overflow: hidden;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            width: 0%;
            transition: width 0.3s;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            display: none;
        }
        .result-success {
            background-color: #e8f5e9;
            border-color: #a5d6a7;
        }
        .result-error {
            background-color: #ffebee;
            border-color: #ef9a9a;
        }
        .status-section {
            margin-top: 30px;
        }
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #1e88e5;
        }
        .status-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 4px;
            background-color: #f5f5f5;
        }
        .status-time {
            color: #757575;
            font-size: 12px;
        }
        .import-summary {
            margin-top: 30px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
        }
        .import-summary h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .import-summary-item {
            margin: 5px 0;
            font-size: 14px;
        }
        .calculation-details {
            margin-top: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .calculation-details h3 {
            margin-top: 0;
            color: #1565c0;
        }
        .calculation-log {
            margin: 10px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-info {
            color: #2196f3;
        }
        .log-warning {
            color: #ff9800;
        }
        .log-error {
            color: #f44336;
        }
        .log-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-item {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid #eee;
        }
        .log-time {
            color: #666;
            font-weight: bold;
        }
        .log-level-INFO {
            color: #2196f3;
        }
        .log-level-WARNING {
            color: #ff9800;
        }
        .log-level-ERROR {
            color: #f44336;
        }
        .log-message {
            margin-left: 10px;
        }
        .connection-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-connected {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .status-connecting {
            background-color: #fff3e0;
            color: #f57c00;
        }
        .status-disconnected {
            background-color: #ffebee;
            color: #c62828;
        }
        .log-controls {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .auto-scroll-toggle {
            font-size: 11px;
            padding: 3px 8px;
            background-color: #e0e0e0;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .auto-scroll-toggle.active {
            background-color: #4caf50;
            color: white;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #757575;
            font-size: 14px;
        }
        .report-section {
            margin-top: 30px;
            padding: 15px;
            background-color: #fff3e0;
            border-left: 4px solid #ffb300;
            border-radius: 4px;
        }
        .report-section h3 { margin: 0 0 10px 0; color: #ef6c00; }
        .report-grid { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .report-grid th, .report-grid td { border: 1px solid #eee; padding: 6px 8px; font-size: 12px; }
        .badge { display: inline-block; padding: 2px 6px; border-radius: 10px; font-size: 11px; }
        .badge-warn { background: #fff3e0; color: #ef6c00; }
        .badge-ok { background: #e8f5e9; color: #2e7d32; }
        .delta-warn { color: #c62828; font-weight: bold; }
        .order-card { margin-top: 12px; border: 1px solid #eee; border-radius: 6px; background: #fff; }
        .order-card .card-header { padding: 8px 10px; background: #fafafa; display:flex; justify-content:space-between; align-items:center; cursor:pointer; }
        .order-card .card-body { padding: 8px 10px; display:none; }
        .pill { background:#e0e0e0; border-radius:12px; padding:2px 8px; font-size:11px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Excel数据导入系统</h1>
        
        <div class="upload-section" id="uploadArea">
            <p>将Excel文件拖放到此处，或</p>
            <input type="file" id="fileInput" accept=".xlsx,.xlsm,.xls" style="display: none;">
            <button class="btn" id="selectFileBtn">选择文件</button>
            <div class="file-info" id="fileInfo"></div>
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button class="btn" id="updateStatusBtn" style="background-color: #4caf50; margin-left: 10px;">手动更新账单状态</button>
        </div>
        
        <div class="result-section" id="resultSection">
            <h3 id="resultTitle"></h3>
            <p id="resultMessage"></p>
        </div>
        
        <div class="import-summary" id="importSummary" style="display: none;">
            <h3>导入摘要</h3>
            <div id="importSummaryContent"></div>
        </div>
        
        <div class="calculation-details" id="calculationDetails" style="display: none;">
            <h3>计算过程</h3>
            <div class="calculation-log" id="calculationLog"></div>
        </div>

        <div class="report-section" id="reportSection" style="display: none;">
            <div style="display:flex;justify-content:space-between;align-items:center;">
                <h3>导入报告</h3>
                <div>
                    <button class="btn" id="downloadJsonBtn" style="font-size:12px;padding:6px 10px;background:#6d4c41;">下载 JSON</button>
                    <button class="btn" id="downloadCsvBtn" style="font-size:12px;padding:6px 10px;margin-left:6px;background:#00897b;">下载 CSV</button>
                    <button class="btn" id="refreshReportBtn" style="font-size:12px;padding:6px 10px;margin-left:6px;background:#fb8c00;">刷新报告</button>
                </div>
            </div>
            <div id="reportMeta"></div>
            <div id="reportSummary"></div>
            <div id="reportIssues"></div>
            <div style="margin-top:10px;">
                <label style="font-size:12px;color:#666;">Delta阈值（高亮 |delta| > 阈值）：</label>
                <input type="number" id="deltaThreshold" value="100" style="width:80px;"/>
                <label style="margin-left:10px;font-size:12px;color:#666;"><input type="checkbox" id="onlyAnomalies"> 仅显示异常</label>
            </div>
            <div id="reportOrders"></div>
        </div>
        
        <div class="status-section">
            <div class="status-title">导入状态</div>
            <div id="statusList">
                <div class="status-item">
                    <div>等待开始导入...</div>
                    <div class="status-time">当前时间: <span id="currentTime"></span></div>
                </div>
            </div>
        </div>
        
        <div class="log-section" id="logSection" style="display: none;">
            <div class="status-title">
                实时日志
                <span class="connection-status status-disconnected" id="connectionStatus">未连接</span>
                <button class="btn" id="toggleLogsBtn" style="float: right; font-size: 12px; padding: 5px 10px;">显示日志</button>
                <button class="btn" id="refreshLogsBtn" style="float: right; font-size: 12px; padding: 5px 10px; margin-right: 10px; background-color: #2196f3;">刷新</button>
            </div>
            <div class="log-controls">
                <button class="auto-scroll-toggle active" id="autoScrollToggle">自动滚动</button>
                <button class="btn" id="clearLogsBtn" style="font-size: 11px; padding: 3px 8px; background-color: #757575;">清空日志</button>
                <span style="font-size: 11px; color: #666;">连接模式: <span id="connectionMode">自动检测</span></span>
            </div>
            <div class="log-container" id="logContainer">
                <div id="logContent">暂无日志...</div>
            </div>
        </div>
        
        <div class="footer">
            <p> 2025 Excel数据导入系统 | 版本 1.0</p>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }
        
        // 初始化时更新时间，并每秒更新一次
        updateCurrentTime();
        setInterval(updateCurrentTime, 1000);
        
        // 文件选择按钮点击事件
        document.getElementById('selectFileBtn').addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });
        
        // 手动更新状态按钮点击事件
        document.getElementById('updateStatusBtn').addEventListener('click', function() {
            updatePaymentStatus();
        });
        
        // 添加日志相关的事件监听器
        const toggleLogsBtn = document.getElementById('toggleLogsBtn');
        const refreshLogsBtn = document.getElementById('refreshLogsBtn');
        const logSection = document.getElementById('logSection');
        const autoScrollToggle = document.getElementById('autoScrollToggle');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionMode = document.getElementById('connectionMode');
        
        let logsVisible = false;
        let logPollingInterval = null;
        let eventSource = null;
        let isSSEConnected = false;
        let autoScroll = true;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        let reconnectTimeout = null;
        
        toggleLogsBtn.addEventListener('click', function() {
            logsVisible = !logsVisible;
            if (logsVisible) {
                logSection.style.display = 'block';
                toggleLogsBtn.textContent = '隐藏日志';
                loadLogs();
                // 开始轮询
                startLogPolling();
            } else {
                logSection.style.display = 'none';
                toggleLogsBtn.textContent = '显示日志';
                // 停止轮询
                stopLogPolling();
            }
        });
        
        refreshLogsBtn.addEventListener('click', loadLogs);
        
        // 自动滚动切换
        autoScrollToggle.addEventListener('click', function() {
            autoScroll = !autoScroll;
            autoScrollToggle.classList.toggle('active', autoScroll);
            autoScrollToggle.textContent = autoScroll ? '自动滚动' : '手动滚动';
        });
        
        // 清空日志
        clearLogsBtn.addEventListener('click', function() {
            document.getElementById('logContent').innerHTML = '<div>日志已清空...</div>';
        });
        
        // 更新连接状态显示
        function updateConnectionStatus(status, mode = null) {
            const statusElement = document.getElementById('connectionStatus');
            const modeElement = document.getElementById('connectionMode');
            
            statusElement.className = 'connection-status';
            
            switch(status) {
                case 'connected':
                    statusElement.classList.add('status-connected');
                    statusElement.textContent = 'SSE已连接';
                    break;
                case 'connecting':
                    statusElement.classList.add('status-connecting');
                    statusElement.textContent = '连接中...';
                    break;
                case 'disconnected':
                    statusElement.classList.add('status-disconnected');
                    statusElement.textContent = '未连接';
                    break;
                case 'polling':
                    statusElement.classList.add('status-connecting');
                    statusElement.textContent = '轮询模式';
                    break;
                case 'error':
                    statusElement.classList.add('status-disconnected');
                    statusElement.textContent = '连接错误';
                    break;
            }
            
            if (mode) {
                modeElement.textContent = mode;
            }
        }
        
        // 文件选择改变事件
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFileSelection(e.target.files[0]);
        });
        
        // 拖放区域事件
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', function() {
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            
            if (e.dataTransfer.files.length) {
                handleFileSelection(e.dataTransfer.files[0]);
            }
        });
        
        // 处理文件选择
        function handleFileSelection(file) {
            if (!file) return;
            
            // 检查文件类型
            const validExtensions = ['.xlsx', '.xlsm', '.xls'];
            const fileName = file.name.toLowerCase();
            const isValid = validExtensions.some(ext => fileName.endsWith(ext));
            
            if (!isValid) {
                showResult(false, '不支持的文件格式', '请上传Excel文件 (.xlsx, .xlsm, .xls)');
                return;
            }
            
            // 显示文件信息
            document.getElementById('fileInfo').textContent = `已选择: ${file.name} (${formatFileSize(file.size)})`;
            
            // 添加状态
            addStatus(`已选择文件: ${file.name}`);
            
            // 上传文件
            uploadFile(file);
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
            else return (bytes / 1048576).toFixed(2) + ' MB';
        }
        
        // 上传文件
        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示进度条
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            
            // 添加状态
            addStatus('开始上传文件...');
            
            // 自动显示日志区域
            if (!logsVisible) {
                logsVisible = true;
                logSection.style.display = 'block';
                toggleLogsBtn.textContent = '隐藏日志';
                loadLogs();
                startLogPolling();
            }
            
            // 发送请求
            const xhr = new XMLHttpRequest();
            
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                    
                    if (percentComplete === 100) {
                        addStatus('文件上传完成，正在处理数据...');
                    }
                }
            });
            
            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        
                        if (response.success) {
                            showResult(true, '导入成功', response.message);
                            addStatus(`导入成功: ${response.message}`);
                            
                            // 刷新日志以显示最新的处理结果
                            if (logsVisible) {
                                loadLogs();
                            }
                            
                            // 显示导入摘要和计算过程
                            console.log('API响应:', response); // 调试信息
                            
                            if (response.import_summary) {
                                showImportSummary(response.import_summary);
                            } else {
                                addStatus('警告: 未收到导入摘要数据');
                                console.warn('未收到导入摘要数据');
                            }

                            // 显示导入报告
                            if (response.import_report) {
                                showImportReport(response.run_id, response.import_report, response.report_path);
                            } else {
                                addStatus('提示: 导入报告暂不可用');
                            }
                            
                            if (response.calculation_logs) {
                                showCalculationLogs(response.calculation_logs);
                            } else {
                                addStatus('警告: 未收到计算日志数据');
                                console.warn('未收到计算日志数据');
                            }
                        } else {
                            showResult(false, '导入失败', response.message);
                            addStatus(`导入失败: ${response.message}`);
                            
                            // 刷新日志以显示错误信息
                            if (logsVisible) {
                                loadLogs();
                            }
                        }
                    } catch (e) {
                        console.error('解析响应失败:', e, xhr.responseText);
                        showResult(false, '导入失败', '解析响应失败');
                        addStatus(`导入失败: 解析响应失败 - ${e.message}`);
                        
                        // 刷新日志以显示错误信息
                        if (logsVisible) {
                            loadLogs();
                        }
                    }
                } else {
                    let errorMsg = '服务器错误';
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMsg = errorResponse.message || errorMsg;
                    } catch (e) {
                        // 如果无法解析JSON，使用默认错误消息
                    }
                    
                    showResult(false, '导入失败', `服务器错误: ${xhr.status}`);
                    addStatus(`导入失败: ${errorMsg} (状态码: ${xhr.status})`);
                    
                    // 刷新日志以显示错误信息
                    if (logsVisible) {
                        loadLogs();
                    }
                }
                
                // 启用上传按钮
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('fileInput').disabled = false;
                
                // 隐藏进度条
                document.getElementById('progressBar').style.display = 'none';
            });
            
            xhr.addEventListener('error', function() {
                console.error('网络错误');
                showResult(false, '导入失败', '网络错误');
                addStatus('导入失败: 网络错误');
                
                // 刷新日志以显示错误信息
                if (logsVisible) {
                    loadLogs();
                }
                
                // 启用上传按钮
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('fileInput').disabled = false;
                
                // 隐藏进度条
                document.getElementById('progressBar').style.display = 'none';
            });
            
            xhr.open('POST', '/api/etl/upload', true);
            xhr.send(formData);
        }
        
        // 显示结果
        function showResult(success, title, message) {
            const resultSection = document.getElementById('resultSection');
            const resultTitle = document.getElementById('resultTitle');
            const resultMessage = document.getElementById('resultMessage');
            
            resultSection.style.display = 'block';
            resultSection.className = 'result-section ' + (success ? 'result-success' : 'result-error');
            resultTitle.textContent = title;
            resultMessage.textContent = message;
        }
        
        // 添加状态
        function addStatus(message) {
            const statusList = document.getElementById('statusList');
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            
            const statusItem = document.createElement('div');
            statusItem.className = 'status-item';
            statusItem.innerHTML = `
                <div>${message}</div>
                <div class="status-time">${timeString}</div>
            `;
            
            statusList.prepend(statusItem);
        }
        
        // 显示导入摘要
        function showImportSummary(summary) {
            const importSummary = document.getElementById('importSummary');
            const importSummaryContent = document.getElementById('importSummaryContent');
            
            let summaryHtml = '';
            if (summary.orders_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">订单: ${summary.orders_count} 条</div>`;
            }
            if (summary.schedules_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">还款计划: ${summary.schedules_count} 条</div>`;
            }
            if (summary.transactions_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">交易: ${summary.transactions_count} 条</div>`;
            }
            if (summary.customers_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">客户信息: ${summary.customers_count} 条</div>`;
            }
            
            importSummaryContent.innerHTML = summaryHtml;
            importSummary.style.display = 'block';
        }
        
        // 显示计算日志
        function showCalculationLogs(logs) {
            const calculationDetails = document.getElementById('calculationDetails');
            const calculationLog = document.getElementById('calculationLog');
            
            // 如果没有日志，显示提示信息
            if (!logs || logs.length === 0) {
                calculationLog.innerHTML = '<div class="log-warning">未找到导入过程日志，请检查日志文件配置</div>';
                calculationDetails.style.display = 'block';
                return;
            }
            
            let logsHtml = '';
            
            // 按时间排序日志
            logs.sort((a, b) => {
                // 尝试解析时间，如果解析失败则按原始顺序
                try {
                    const timeA = new Date(a.time);
                    const timeB = new Date(b.time);
                    return timeA - timeB;
                } catch (e) {
                    return 0;
                }
            });
            
            logs.forEach(log => {
                let logClass = 'log-info';
                if (log.level === 'WARNING') {
                    logClass = 'log-warning';
                } else if (log.level === 'ERROR') {
                    logClass = 'log-error';
                }
                
                // 格式化日志消息，确保时间和级别正确显示
                const timeStr = log.time || '未知时间';
                const levelStr = log.level || 'INFO';
                const messageStr = log.message || '无消息内容';
                
                logsHtml += `<div class="${logClass}">[${timeStr}] ${levelStr}: ${messageStr}</div>`;
            });
            
            calculationLog.innerHTML = logsHtml;
            calculationDetails.style.display = 'block';
        }

        // 显示导入报告
        function showImportReport(runId, report, reportPath) {
            const section = document.getElementById('reportSection');
            const meta = document.getElementById('reportMeta');
            const summary = document.getElementById('reportSummary');
            const issues = document.getElementById('reportIssues');
            window._lastRunId = runId || null;

            section.style.display = 'block';
            meta.innerHTML = `运行ID: <code>${runId || '-'}</code> ${reportPath ? ` | 文件: <code>${reportPath}</code>` : ''}`;

            const s = report.summary || {};
            summary.innerHTML = `
                <div class="import-summary-item">订单数: ${s.total_orders ?? '-'}，计划数: ${s.total_schedules ?? '-'}</div>
                <div class="import-summary-item">合同校验: <span class="badge ${s.contract_check_fail>0?'badge-warn':'badge-ok'}">通过 ${s.contract_check_pass||0} / 失败 ${s.contract_check_fail||0}</span></div>
            `;

            const iss = report.issues || [];
            if (iss.length === 0) {
                issues.innerHTML = '<div class="import-summary-item">无问题项</div>';
            } else {
                let html = '<table class="report-grid"><thead><tr><th>订单号</th><th>类型</th><th>级别</th><th>说明</th></tr></thead><tbody>';
                iss.slice(0, 50).forEach(it => {
                    html += `<tr><td>${it.order_number||''}</td><td>${it.type||''}</td><td>${it.severity||''}</td><td>${it.message||''}</td></tr>`;
                });
                html += '</tbody></table>';
                issues.innerHTML = html;
            }

            renderOrderSchedules(report);
            document.getElementById('deltaThreshold').addEventListener('change', ()=>renderOrderSchedules(report));
            document.getElementById('onlyAnomalies').addEventListener('change', ()=>renderOrderSchedules(report));
        }

        // 刷新最新报告
        document.getElementById('refreshReportBtn').addEventListener('click', function() {
            fetch('/api/etl/report?last=true', { method: 'GET' })
            .then(r => r.json())
            .then(data => {
                if (data && data.success) {
                    showImportReport(data.run_id, data.report, null);
                    addStatus('已刷新最新导入报告');
                } else {
                    addStatus('刷新导入报告失败: ' + (data && data.message ? data.message : '未知错误'));
                }
            })
            .catch(err => {
                console.error('刷新报告失败', err);
                addStatus('刷新导入报告失败: 网络错误');
            });
        });

        // 下载报告 JSON/CSV
        document.getElementById('downloadJsonBtn').addEventListener('click', function() {
            const rid = window._lastRunId;
            const url = rid ? `/api/etl/report/download?run_id=${encodeURIComponent(rid)}&format=json` : '/api/etl/report/download?last=true&format=json';
            window.open(url, '_blank');
        });
        document.getElementById('downloadCsvBtn').addEventListener('click', function() {
            const rid = window._lastRunId;
            const url = rid ? `/api/etl/report/download?run_id=${encodeURIComponent(rid)}&format=csv` : '/api/etl/report/download?last=true&format=csv';
            window.open(url, '_blank');
        });

        function renderOrderSchedules(report){
            const container = document.getElementById('reportOrders');
            const threshold = parseFloat(document.getElementById('deltaThreshold').value || '100') || 100;
            const only = document.getElementById('onlyAnomalies').checked;
            const orders = report.orders || [];
            let html = '';
            orders.forEach(o => {
                // build rows
                let rows = '';
                (o.schedules||[]).forEach(s => {
                    const delta = Math.abs(parseFloat(s.delta_amount||0));
                    const isWarn = delta > threshold;
                    if (only && !isWarn) return;
                    rows += `
                        <tr class="${isWarn?'delta-warn':''}">
                            <td>${s.period ?? ''}</td>
                            <td>${s.due_date ?? ''}</td>
                            <td>${s.expected_amount ?? ''}</td>
                            <td>${s.paid_amount ?? ''}</td>
                            <td>${(s.delta_amount ?? '')}</td>
                            <td>${s.status ?? ''}</td>
                        </tr>
                    `;
                });
                if (!rows) return; // skip empty after filter when only anomalies
                html += `
                    <div class="order-card">
                        <div class="card-header" onclick="this.nextElementSibling.style.display = (this.nextElementSibling.style.display==='block'?'none':'block')">
                            <div>
                                <strong>${o.order_number||''}</strong>
                                <span class="pill">${o.product_type||''}</span>
                                ${o.periods?`<span class="pill">期数:${o.periods}</span>`:''}
                            </div>
                            <div style="font-size:12px;color:#666;">台数:${o.devices||'-'}</div>
                        </div>
                        <div class="card-body">
                            <table class="report-grid">
                                <thead><tr><th>期次</th><th>到期日</th><th>应还</th><th>实还</th><th>Delta</th><th>状态</th></tr></thead>
                                <tbody>${rows}</tbody>
                            </table>
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html || '<div class="import-summary-item">暂无可显示的期次数据（调整阈值或取消仅异常）</div>';
        }
        
        // 日志相关函数
        function loadLogs() {
            fetch('/api/etl/logs?lines=50')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLogs(data.logs);
                    } else {
                        document.getElementById('logContent').innerHTML = '<div class="log-error">加载日志失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载日志错误:', error);
                    document.getElementById('logContent').innerHTML = '<div class="log-error">加载日志时发生网络错误</div>';
                });
        }
        
        function startSSEConnection() {
            if (eventSource) {
                eventSource.close();
            }
            
            if (reconnectTimeout) {
                clearTimeout(reconnectTimeout);
                reconnectTimeout = null;
            }
            
            console.log('尝试建立SSE连接... (尝试次数: ' + (reconnectAttempts + 1) + ')');
            updateConnectionStatus('connecting', 'SSE模式');
            
            eventSource = new EventSource('/api/etl/logs/stream');
            
            eventSource.onopen = function(event) {
                console.log('SSE连接已建立');
                isSSEConnected = true;
                reconnectAttempts = 0; // 重置重连计数
                updateConnectionStatus('connected', 'SSE模式');
                
                // 停止轮询，使用SSE
                if (logPollingInterval) {
                    clearInterval(logPollingInterval);
                    logPollingInterval = null;
                }
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const logEntry = JSON.parse(event.data);
                    if (logEntry.type === 'heartbeat') {
                        // 心跳消息，保持连接活跃
                        console.log('收到心跳消息');
                        return;
                    }
                    
                    console.log('收到日志消息:', logEntry);
                    // 添加新的日志条目到显示区域
                    appendLogEntry(logEntry);
                } catch (e) {
                    console.error('解析SSE数据错误:', e, event.data);
                    // 显示原始数据作为日志
                    appendLogEntry({
                        time: new Date().toLocaleTimeString(),
                        level: 'INFO',
                        message: event.data
                    });
                }
            };
            
            eventSource.onerror = function(event) {
                console.error('SSE连接错误:', event);
                console.log('EventSource readyState:', eventSource.readyState);
                isSSEConnected = false;
                
                // 检查连接状态
                if (eventSource.readyState === EventSource.CLOSED) {
                    console.log('SSE连接已关闭');
                    updateConnectionStatus('disconnected');
                    
                    // 尝试重连
                    if (reconnectAttempts < maxReconnectAttempts && logsVisible) {
                        reconnectAttempts++;
                        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts - 1), 10000); // 指数退避，最大10秒
                        console.log(`将在 ${delay}ms 后尝试重连...`);
                        
                        reconnectTimeout = setTimeout(() => {
                            if (logsVisible && !isSSEConnected) {
                                startSSEConnection();
                            }
                        }, delay);
                    } else {
                        // 重连次数用完，回退到轮询模式
                        console.log('SSE重连失败，回退到轮询模式');
                        updateConnectionStatus('polling', '轮询模式');
                        if (logsVisible) {
                            logPollingInterval = setInterval(loadLogs, 5000);
                        }
                    }
                } else if (eventSource.readyState === EventSource.CONNECTING) {
                    console.log('SSE正在重连...');
                    updateConnectionStatus('connecting');
                }
            };
        }
        
        function stopSSEConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                isSSEConnected = false;
            }
            
            if (reconnectTimeout) {
                clearTimeout(reconnectTimeout);
                reconnectTimeout = null;
            }
            
            reconnectAttempts = 0;
            updateConnectionStatus('disconnected');
        }
        
        function appendLogEntry(logEntry) {
            const logContent = document.getElementById('logContent');
            const logContainer = document.getElementById('logContainer');
            
            // 创建新的日志条目
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `
                <span class="log-time">${logEntry.time}</span>
                <span class="log-level-${logEntry.level}">[${logEntry.level}]</span>
                <span class="log-message">${logEntry.message}</span>
            `;
            
            // 如果当前没有日志，清空"暂无日志"提示
            if (logContent.innerHTML.includes('暂无日志') || logContent.innerHTML.includes('日志已清空')) {
                logContent.innerHTML = '';
            }
            
            // 添加新日志条目
            logContent.appendChild(logItem);
            
            // 限制显示的日志条目数量（保持最新的100条）
            const logItems = logContent.querySelectorAll('.log-item');
            if (logItems.length > 100) {
                logContent.removeChild(logItems[0]);
            }
            
            // 自动滚动到底部（仅在启用自动滚动时）
            if (autoScroll) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }
        
        function displayLogs(logs) {
            const logContent = document.getElementById('logContent');
            if (!logs || logs.length === 0) {
                logContent.innerHTML = '<div>暂无日志...</div>';
                return;
            }
            
            let html = '';
            logs.forEach(log => {
                html += `
                    <div class="log-item">
                        <span class="log-time">${log.time}</span>
                        <span class="log-level-${log.level}">[${log.level}]</span>
                        <span class="log-message">${log.message}</span>
                    </div>
                `;
            });
            
            logContent.innerHTML = html;
            // 自动滚动到底部（仅在启用自动滚动时）
            if (autoScroll) {
                const logContainer = document.getElementById('logContainer');
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }
        
        function startLogPolling() {
            // 重置重连计数
            reconnectAttempts = 0;
            
            // 优先尝试使用SSE
            if (typeof(EventSource) !== "undefined") {
                console.log('浏览器支持SSE，尝试建立SSE连接');
                startSSEConnection();
            } else {
                // 浏览器不支持SSE，使用轮询
                console.log('浏览器不支持SSE，使用轮询模式');
                updateConnectionStatus('polling', '轮询模式（浏览器不支持SSE）');
                logPollingInterval = setInterval(loadLogs, 5000);
            }
        }
        
        function stopLogPolling() {
            if (logPollingInterval) {
                clearInterval(logPollingInterval);
                logPollingInterval = null;
            }
            stopSSEConnection();
        }
        
        // 手动更新账单状态
        function updatePaymentStatus() {
            const updateBtn = document.getElementById('updateStatusBtn');
            
            // 禁用按钮，防止重复点击
            updateBtn.disabled = true;
            updateBtn.textContent = '正在更新...';
            
            addStatus('开始手动更新账单状态...');
            
            // 自动显示日志区域
            if (!logsVisible) {
                logsVisible = true;
                logSection.style.display = 'block';
                toggleLogsBtn.textContent = '隐藏日志';
                loadLogs();
                startLogPolling();
            }
            
            // 发送更新请求
            fetch('/api/etl/update-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(true, '更新成功', data.message);
                    addStatus(`状态更新成功: ${data.message}`);
                    // 立即刷新日志以显示最新状态
                    setTimeout(loadLogs, 1000);
                } else {
                    showResult(false, '更新失败', data.message);
                    addStatus(`状态更新失败: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('更新状态失败:', error);
                showResult(false, '更新失败', '网络错误或服务器异常');
                addStatus('状态更新失败: 网络错误或服务器异常');
            })
            .finally(() => {
                // 恢复按钮状态
                updateBtn.disabled = false;
                updateBtn.textContent = '手动更新账单状态';
            });
        }
    </script>
</body>
</html>
