import os
import re
import csv
from datetime import date
from typing import Optional, Dict, Tuple
from dateutil.relativedelta import relativedelta


def _normalize_text(s: str) -> str:
    if s is None:
        return ''
    return re.sub(r"\s+", "", str(s)).lower()


def normalize_model(s: str) -> str:
    if s is None:
        return ''
    x = str(s).strip().lower()
    x = re.sub(r"[\s\-_/\\.]+", "", x)
    x = x.replace('pro max', 'promax').replace('promax', 'promax')
    x = x.replace('gb', 'g')
    x = x.replace('xvi', '16').replace('xv', '15').replace('xiv', '14').replace('xiii', '13').replace('xii', '12').replace('xi', '11').replace('x', '10')
    return x


def load_pricing_csv():
    candidates = [
        os.path.join(os.getcwd(), 'config', 'pricing.csv'),
        os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'pricing.csv'),
    ]
    path = None
    for p in candidates:
        p = os.path.abspath(p)
        if os.path.exists(p):
            path = p
            break
    rows = []
    if not path:
        return rows
    with open(path, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            row['product_type_norm'] = _normalize_text(row.get('product_type'))
            row['model_norm_norm'] = normalize_model(row.get('model_norm'))
            row['model_aliases_norm'] = [normalize_model(x) for x in (row.get('model_aliases') or '').split(';') if x]
            def to_float(v, d=None):
                try:
                    return float(v)
                except Exception:
                    return d
            def to_int(v, d=None):
                try:
                    return int(v)
                except Exception:
                    return d
            row['periods_int'] = to_int(row.get('periods'))
            row['billing_months_int'] = to_int(row.get('billing_months'))
            row['rent_per_period_float'] = to_float(row.get('rent_per_period'))
            row['buyout_total_float'] = to_float(row.get('buyout_total'), 0.0)
            row['downpayment_default_float'] = to_float(row.get('downpayment_default'), 0.0)
            rows.append(row)
    return rows


PRICING = load_pricing_csv()


def find_pricing(product_type: str, model: str, periods: int):
    pt = _normalize_text(product_type)
    mn = normalize_model(model)
    for row in PRICING:
        if row['product_type_norm'] == pt and row['periods_int'] == periods and row['model_norm_norm'] == mn:
            return row
    for row in PRICING:
        if row['product_type_norm'] == pt and row['periods_int'] == periods and mn in row['model_aliases_norm']:
            return row
    for row in PRICING:
        if row['product_type_norm'] == pt and row['periods_int'] == periods:
            return row
    return None


def compute_expected_amounts(product_type: str, model: str, periods: int, devices_count: int,
                              rent_per_period: Optional[float], downpayment: Optional[float], buyout_total: Optional[float]) -> Tuple[Dict[int, float], Dict]:
    """Return (expected_amounts: dict[int,float], meta: dict) without DB.
    Implements 4+2 and 6期 lease rules with pricing fallbacks.
    """
    expected = {}
    meta = {
        'rent_estimated': False,
        'downpayment_estimated': False,
        'buyout_estimated': False,
        'pricing_hit': False,
        'normalized_model': normalize_model(model),
    }
    devices = devices_count or 1
    is_lease = '租' in (product_type or '')
    pr = None
    if rent_per_period is None or downpayment is None or buyout_total is None:
        pr = find_pricing(product_type, model, periods or 0)
        if pr:
            meta['pricing_hit'] = True
    if rent_per_period is None and pr and pr.get('rent_per_period_float') is not None:
        rent_per_period = pr['rent_per_period_float'] * devices
        meta['rent_estimated'] = True
    if downpayment is None and pr:
        d = pr.get('downpayment_default_float')
        if d is not None:
            downpayment = d * devices
            meta['downpayment_estimated'] = True
    if buyout_total is None and pr:
        b = pr.get('buyout_total_float')
        if b is not None:
            buyout_total = b * devices
            meta['buyout_estimated'] = True

    if is_lease and periods == 4:
        if rent_per_period is not None:
            for p in range(1, 5):
                expected[p] = rent_per_period
        if buyout_total is not None:
            half = buyout_total / 2.0
            dp = downpayment or 0.0
            p5 = half
            p6 = half - dp
            if p6 < 0:
                p6 = 0.0
                p5 = max(buyout_total - dp, 0.0)
            expected[5] = p5
            expected[6] = p6
    elif is_lease and periods == 6:
        if rent_per_period is not None:
            for p in range(1, 6):
                expected[p] = rent_per_period
            if buyout_total is not None:
                expected[6] = max(rent_per_period - buyout_total, 0.0)
            else:
                expected[6] = rent_per_period
    else:
        if rent_per_period is not None:
            for p in range(1, (periods or 0) + 1):
                expected[p] = rent_per_period
    return expected, meta


def infer_p56_dates(base_date: date):
    return {
        5: base_date + relativedelta(months=+1),
        6: base_date + relativedelta(months=+2),
    }
