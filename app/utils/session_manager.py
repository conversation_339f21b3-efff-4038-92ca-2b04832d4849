# app/utils/session_manager.py
# 数据库会话管理模块

import logging
import functools
import time
import traceback
from contextlib import contextmanager
from sqlalchemy.exc import SQLAlchemyError
from app.routes.db import get_db_session, close_db_session

# 创建专用的日志记录器
logger = logging.getLogger(__name__)


@contextmanager
def session_scope():
    """提供事务范围的会话上下文管理器
    
    使用示例:
    with session_scope() as session:
        # 使用session进行数据库操作
        # 如果没有异常抛出，事务会自动提交
        # 如果有异常抛出，事务会自动回滚
    
    Returns:
        SQLAlchemy Session对象
    """
    session = get_db_session()
    start_time = time.time()
    try:
        logger.debug("数据库会话已创建")
        yield session
        session.commit()
        logger.debug(f"事务已提交，会话执行时间: {time.time() - start_time:.3f}秒")
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"SQL错误，事务已回滚: {str(e)}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"非SQL错误，事务已回滚: {str(e)}")
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise
    finally:
        close_db_session(session)
        logger.debug(f"数据库会话已关闭，总耗时: {time.time() - start_time:.3f}秒")


def with_session(func):
    """装饰器，为函数提供数据库会话
    
    使用示例:
    @with_session
    def some_function(session, *args, **kwargs):
        # 使用session进行数据库操作
        # 会话会在函数返回或异常时自动处理
    
    Args:
        func: 需要装饰的函数，第一个参数必须是session
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with session_scope() as session:
            return func(session, *args, **kwargs)
    return wrapper


def query_timer(func):
    """装饰器，用于记录查询执行时间
    
    使用示例:
    @query_timer
    def some_query_function(*args, **kwargs):
        # 执行数据库查询操作
        
    Args:
        func: 需要记录执行时间的函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"查询 {func.__name__} 执行时间: {duration:.3f}秒")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"查询 {func.__name__} 出错，耗时: {duration:.3f}秒")
            logger.error(f"错误信息: {str(e)}")
            raise
    return wrapper
