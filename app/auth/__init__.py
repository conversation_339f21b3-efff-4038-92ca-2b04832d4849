# app/__init__.py

from flask import Flask, jsonify
from app.config import Config, DevelopmentConfig, ProductionConfig
from app.utils.logging_config import setup_logging
import logging

def create_app():
    app = Flask(__name__)

    # 选择配置类
    env = app.env
    if env == 'production':
        app.config.from_object(ProductionConfig)
    else:
        app.config.from_object(DevelopmentConfig)

    # 设置日志
    setup_logging()

    # 注册蓝图
    register_blueprints(app)

    # 全局错误处理
    @app.errorhandler(500)
    def internal_error(error):
        logging.error(f'服务器内部错误: {error}')
        return jsonify({'error': '服务器内部错误，请联系管理员。'}), 500

    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({'error': '资源未找到。'}), 404

    return app
