product_type,model_norm,model_aliases,periods,billing_months,scheme_version,rent_per_period,buyout_total,downpayment_default,currency,effective_from,effective_to,shop,remarks
# 示例：电商产品（每期还款金 1979.9，台数请在应用中按台数相乘；如无买断金可填0）
电商,iPhone16 Pro Max 512G,"iPhone16ProMax512G;iPhone 16 Pro Max 512G",6,6,电商,1979.9,0,119.6,CNY,2025-01-01,,ALL,"示例：电商主力机型；如实际无首付可改为0"
# 示例：旧租赁（6期，尾期减买断金）
租赁,iPhone16 Pro Max 512G,"iPhone16ProMax512G;iPhone 16 Pro Max 512G",6,6,旧租赁6期,2274,116,610,CNY,2025-01-01,,ALL,"第1-5期=2274；第6期=2274-116；首付款按交易入账计入第1期"
# 示例：新租赁（4+2，买断金两次分摊，尾期抵扣首付）
租赁,iPhone16 Pro Max 512G,"iPhone16ProMax512G;iPhone 16 Pro Max 512G",4,6,新租赁4+2,2389.5,4202,610,CNY,2025-01-01,,ALL,"第1-4期=2389.5；第5期=4202/2；第6期=4202/2-首付；不足0按0，差额归并第5期或记录告警"
# 可选：通配/默认配置（匹配不到具体机型时作为兜底），按需启用并在代码中支持匹配优先级
# 租赁,*,*,4,6,新租赁4+2,2389.5,4200,600,CNY,2025-01-01,,ALL,"默认兜底配置（示例），请按需修改或删除"
