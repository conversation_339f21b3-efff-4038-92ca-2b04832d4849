# Repository Guidelines

## Project Structure & Module Organization
- `app/` — Flask app: `routes/` (blueprints, e.g., `summary_data_db.py`), `services/` (business logic), `utils/` (helpers & logging), `auth/`, `templates/`, `config.py`.
- `app/routes/db/` — DB models, queries, and migrations.
- `scripts/` — Demo and API verification scripts (non-production).
- `config/`, `instance/` (holds `secret_key`), `logs/`, `reports/`.
- Entrypoints: `run.py` (simple run), `start_dev.py` (dev), `docker-compose*.yml` (containers).
- `tests/` — Place new tests here (currently minimal).

## Build, Test, and Development Commands
- Setup: `python -m venv venv && source venv/bin/activate && pip install -r requirements.txt`
- Run (dev): `python start_dev.py` or `python run.py`
- Run (prod): `gunicorn -w 4 -b 0.0.0.0:5000 "run:app"`
- Docker (default/dev): `docker-compose up -d` or `docker-compose -f docker-compose.dev.yml up`
- DB migrations/indexes: `python run_migrations.py`
- Type-check (optional): `mypy app` (if installed locally)

## Coding Style & Naming Conventions
- Python, 4-space indentation, UTF-8.
- Naming: `snake_case` for functions/vars, `PascalCase` for classes, `CONSTANT_CASE` for constants.
- Imports: prefer absolute (e.g., `from app.services.summary_service import ...`).
- Routes go in `app/routes/*.py` with a `bp` blueprint; keep business logic in `app/services/`; reusable helpers in `app/utils/`.

## Testing Guidelines
- Preferred: `pytest` with Flask test client: `from app import create_app`.
- Place files as `tests/test_<area>_<behavior>.py` (e.g., `tests/test_auth_login.py`).
- Aim for critical-path coverage of routes and services; mock DB where reasonable.
- Run: `pytest -q` (add `pytest` to your environment if not present).

## Commit & Pull Request Guidelines
- Follow Conventional Commits found in this repo: `feat:`, `fix:`, `docs:`, `refactor:`, `perf:`, `chore:`, `build:` (scopes like `db`, `api`, `etl`).
- Commits: present tense, concise summary; include scope when helpful.
- PRs: clear description, linked issues, steps to reproduce/test, and screenshots for UI/templates where applicable.

## Security & Configuration Tips
- Do not commit secrets. Use `.env` (see `env.example`) or env vars: e.g., `DATABASE_URI`, `SECRET_KEY`.
- `SECRET_KEY` persists in `instance/secret_key` (auto-managed); keep `instance/` out of VCS.
- Keep logs in `logs/`; avoid large artifacts in commits.
- Validate file uploads and sanitize inputs in routes.

