version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: flask-postgres
    environment:
      POSTGRES_DB: flask_db
      POSTGRES_USER: flask_user
      POSTGRES_PASSWORD: flask_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      TZ: Asia/Shanghai  # 设置数据库时区
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d  # 初始化脚本目录
    ports:
      - "5433:5432"
    restart: unless-stopped
    networks:
      - flask-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flask_user -d flask_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  flask-api:
    build:
      context: .
      dockerfile: Dockerfile
    image: flask_api:latest
    container_name: flask-api-container
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URI=****************************************************/flask_db
      - UPLOAD_FOLDER=uploads
      - ETL_LOG_FILE=logs/etl.log
      - TZ=Asia/Shanghai  # 设置容器时区
    volumes:
      # 持久化上传文件
      - ./uploads:/app/uploads
      # 持久化日志文件
      - ./logs:/app/logs
      # 持久化实例文件夹
      - ./instance:/app/instance
      # 备份目录（可选）
      - ./backups:/app/backups
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - flask-network

networks:
  flask-network:
    driver: bridge

volumes:
  postgres_data:

# 开发环境配置说明:
# 如果需要本地开发，创建 docker-compose.dev.yml 并修改以下配置:
# - 使用本地代码挂载: ./:/app
# - 使用开发环境变量: FLASK_ENV=development
# - 添加调试端口映射等