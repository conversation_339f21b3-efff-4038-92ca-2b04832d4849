# Flask API 项目

## 项目简介
这是一个基于 Flask 框架开发的 API 项目，主要用于处理 Excel 数据相关的业务逻辑。项目提供了数据导入导出、用户认证等功能，支持开发和生产环境的配置切换。

## 📚 文档导航

- **[README.md](README.md)** - 项目概述和快速开始
- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - 完整部署指南（Docker + 服务器）
- **[TECHNICAL_SOLUTIONS.md](TECHNICAL_SOLUTIONS.md)** - 技术问题解决方案汇总
- **[数据库接口文档.md](数据库接口文档.md)** - API接口文档
- **[数据库表结构与关系汇总.md](数据库表结构与关系汇总.md)** - 数据库结构文档
- **[OPTIMIZATION_ROADMAP.md](OPTIMIZATION_ROADMAP.md)** - 项目优化路线图
- **[CLAUDE.md](CLAUDE.md)** - 重要项目文档

## 技术栈
- **后端框架**: Flask 2.3.2
- **数据处理**: openpyxl 3.1.2
- **环境配置**: python-dotenv 1.0.0
- **生产部署**: gunicorn 20.1.0
- **Python版本**: 3.x

## 项目结构
```
flask_api/
├── app/                    # 应用主目录
│   ├── auth/              # 认证相关模块
│   ├── models/            # 数据模型
│   ├── routes/            # 路由处理
│   ├── utils/             # 工具函数
│   ├── config.py          # 配置文件
│   └── __init__.py        # 应用初始化
├── excel备份/             # Excel 文件备份目录
├── logs/                  # 日志文件目录
├── venv/                  # Python 虚拟环境
├── .env                   # 环境变量配置
├── .gitignore            # Git 忽略文件配置
├── .cursorignore         # Cursor 索引配置
├── requirements.txt       # 项目依赖
├── run.py                # 应用启动文件
└── README.md             # 项目说明文档
```

## 环境要求
- Python 3.x
- Flask 2.3.2
- openpyxl 3.1.2
- python-dotenv 1.0.0
- gunicorn 20.1.0（生产环境可选）

## 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
```

2. 创建虚拟环境
```bash
python -m venv venv
```

3. 激活虚拟环境
```bash
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

4. 安装依赖
```bash
pip install -r requirements.txt
```

5. 配置环境变量
创建 `.env` 文件并配置以下内容：
```env
API_KEY=your_api_key
EXCEL_FILE_PATH=/path/to/excel/file
LOG_FILE_PATH=/path/to/log/file
```

## 运行项目
### 开发环境
```bash
python run.py
```

### 生产环境
```bash
gunicorn -w 4 -b 0.0.0.0:5000 "run:app"
```

## 主要功能
- Excel 数据处理和导入导出
- 用户认证和授权
- API 接口服务
- 日志记录和监控
- 多环境配置支持

## 配置说明
项目支持两种运行环境：
- **开发环境**：DEBUG=True，显示详细错误信息
- **生产环境**：DEBUG=False，优化性能和安全

## 注意事项
- 请确保 Excel 文件格式正确
- 定期备份重要数据
- 注意保护敏感信息（API_KEY等）
- 生产环境部署时注意配置安全设置
- 定期检查日志文件大小，避免磁盘空间不足

## 维护说明
- 日志文件位于 `logs/` 目录
- Excel 备份文件位于 `excel备份/` 目录
- 定期检查日志文件大小
- 监控 API 访问频率和性能

## 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证
[添加许可证信息]

## 联系方式
[添加联系方式]
