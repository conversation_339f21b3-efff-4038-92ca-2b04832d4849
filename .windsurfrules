# Excel到数据库实时同步解决方案

## 目录
- [1. 项目背景与目标](#1-项目背景与目标)
- [2. 数据库设计](#2-数据库设计)
  - [2.1 数据库模型设计](#21-数据库模型设计)
  - [2.2 数据库索引设计](#22-数据库索引设计)
  - [2.3 数据关系图](#23-数据关系图)
- [3. 数据同步系统](#3-数据同步系统)
  - [3.1 同步服务设计](#31-同步服务设计)
  - [3.2 同步触发机制](#32-同步触发机制)
  - [3.3 数据验证机制](#33-数据验证机制)
- [4. API层改造](#4-api层改造)
  - [4.1 改造策略](#41-改造策略)
  - [4.2 API适配器模式](#42-api适配器模式)
  - [4.3 示例API改造](#43-示例api改造)
- [5. 实施阶段](#5-实施阶段)
  - [5.1 准备阶段](#51-准备阶段)
  - [5.2 过渡阶段](#52-过渡阶段)
  - [5.3 完全迁移阶段](#53-完全迁移阶段)
- [6. 部署与维护](#6-部署与维护)
  - [6.1 部署步骤](#61-部署步骤)
  - [6.2 监控与报警](#62-监控与报警)
  - [6.3 备份策略](#63-备份策略)
- [7. 性能优化](#7-性能优化)
  - [7.1 数据库优化](#71-数据库优化)
  - [7.2 查询优化](#72-查询优化)
  - [7.3 增量同步策略](#73-增量同步策略)
- [8. 附录](#8-附录)
  - [8.1 技术栈选择](#81-技术栈选择)
  - [8.2 常见问题与解决方案](#82-常见问题与解决方案)

## 1. 项目背景与目标

本项目基于Flask框架开发的API系统，主要处理Excel数据中的订单管理、资金流水和客户信息。随着业务规模的增长，直接从Excel读取数据已经影响到系统效率，但由于跨部门协作的业务模式，对方只能提供Excel表格作为数据源。

项目目标：
- 保持业务流程不变，继续接收Excel作为数据输入源
- 构建实时Excel到数据库的同步机制
- 将API接口从直接读取Excel转为查询数据库
- 提高系统响应速度和稳定性
- 支持更复杂的数据查询和分析需求

Excel表格结构：
- **订单管理**：日期、订单编号、客户姓名、型号、客户属性、用途、还款周期、产品、期数等
- **资金流水账**：日期、订单编号、客户姓名、型号、客户属性、用途、交易金额、交易类型等
- **@芳会资料补充**：日期、订单编号、客户、手机号码、租期、客服、业务、备注

## 2. 数据库设计

### 2.1 数据库模型设计

基于Excel表格结构，设计相应的数据库模型：

**1. 订单表 (orders)**
```python
class Order(Base):
    __tablename__ = 'orders'
    
    id = Column(Integer, primary_key=True)
    order_date = Column(Date, index=True)
    order_number = Column(String(50), unique=True, index=True)
    customer_name = Column(String(100), index=True)
    model = Column(String(100))
    customer_attribute = Column(String(50))
    usage = Column(String(100))
    payment_cycle = Column(String(50))
    product_type = Column(String(50), index=True)
    periods = Column(Integer)
    business_type = Column(String(50))
    total_receivable = Column(Float)
    current_receivable = Column(Float)
    remarks = Column(Text)
    cost = Column(Float)
    shop_affiliation = Column(String(100))
    
    payment_schedules = relationship('PaymentSchedule', back_populates='order')
    transactions = relationship('Transaction', back_populates='order')
    customer_info = relationship('CustomerInfo', back_populates='order', uselist=False)
```

**2. 还款计划表 (payment_schedules)**
```python
class PaymentSchedule(Base):
    __tablename__ = 'payment_schedules'
    
    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id'), index=True)
    period_number = Column(Integer)
    due_date = Column(Date)
    amount = Column(Float)
    status = Column(String(20))  # 未到期、按时还款、逾期未还、逾期还款、提前还款
    
    order = relationship('Order', back_populates='payment_schedules')
```

**3. 交易表 (transactions)**
```python
class Transaction(Base):
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True)
    transaction_date = Column(Date, index=True)
    order_id = Column(Integer, ForeignKey('orders.id'), index=True)
    customer_name = Column(String(100))
    model = Column(String(100))
    customer_attribute = Column(String(50))
    usage = Column(String(100))
    payment_cycle = Column(String(50))
    product = Column(String(50))
    amount = Column(Float)
    period_number = Column(String(50))
    transaction_type = Column(String(50), index=True)
    direction = Column(String(50))
    transaction_order_number = Column(String(50))
    available_balance = Column(Float)
    pending_withdrawal = Column(Float)
    remarks = Column(Text)
    
    order = relationship('Order', back_populates='transactions')
```

**4. 客户信息表 (customer_info)**
```python
class CustomerInfo(Base):
    __tablename__ = 'customer_info'
    
    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id'), unique=True, index=True)
    order_number = Column(String(50), unique=True, index=True)
    customer_name = Column(String(100))
    phone = Column(String(20))
    rental_period = Column(String(50))
    customer_service = Column(String(50))
    business_affiliation = Column(String(50))
    remarks = Column(Text)
    
    order = relationship('Order', back_populates='customer_info')
```

### 2.2 数据库索引设计

为提高查询性能，针对以下字段创建索引：

1. **订单表**：
   - `order_date`：按日期查询订单
   - `order_number`：唯一索引，精确查找订单
   - `customer_name`：客户名称查询
   - `product_type`：按产品类型筛选

2. **还款计划表**：
   - `order_id`：关联订单查询

3. **交易表**：
   - `transaction_date`：按日期查询交易
   - `order_id`：关联订单查询
   - `transaction_type`：按交易类型筛选

4. **客户信息表**：
   - `order_id`：关联订单查询
   - `order_number`：唯一索引，快速查找

### 2.3 数据关系图

```
+-------------+       +-------------------+
|   订单表     |       |    还款计划表     |
| (orders)    |<----->| (payment_schedules)|
+-------------+       +-------------------+
      ^
      |
      |
+-------------+       +-------------------+
|   交易表     |       |    客户信息表     |
|(transactions)|<---->| (customer_info)   |
+-------------+       +-------------------+
```

## 3. 数据同步系统

### 3.1 同步服务设计

创建专门的同步服务类，负责从Excel读取数据并写入数据库：

```python
class ExcelSyncService:
    def __init__(self, db_session, excel_file_path):
        self.db_session = db_session
        self.excel_file_path = excel_file_path
        self.logger = logging.getLogger(__name__)
        
    def sync_all(self):
        """执行全量同步"""
        try:
            # 从Excel加载数据
            wb = load_workbook_with_error_handling(self.excel_file_path, read_only=False, data_only=True)
            if wb is None:
                self.logger.error("无法加载Excel文件")
                return False
                
            # 开始事务
            with self.db_session.begin():
                # 清空原有数据（也可以做增量更新）
                self._clear_all_data()
                
                # 同步各表数据
                self._sync_orders(wb)
                self._sync_transactions(wb)
                self._sync_customer_info(wb)
                
            self.logger.info("数据同步完成")
            return True
            
        except Exception as e:
            self.logger.error(f"同步过程中出错: {str(e)}")
            return False
```

### 3.2 同步触发机制

设计两种同步触发方式：

**1. 定时任务同步**
```python
def setup_scheduler():
    """设置定时任务调度器"""
    scheduler = BackgroundScheduler()
    
    # 添加定时同步任务，由配置决定间隔
    scheduler.add_job(
        sync_excel_to_db,
        'interval', 
        seconds=Config.SYNC_INTERVAL,
        id='excel_sync_job'
    )
    
    # 启动调度器
    scheduler.start()
    logging.info("定时任务调度器已启动")
    return scheduler
```

**2. 手动触发同步 API**
```python
@bp.route('/sync_excel', methods=['POST'])
@require_api_key('sync_excel')
def sync_excel():
    """手动触发Excel到数据库的同步"""
    logging.info("手动触发Excel同步")
    try:
        session = get_db_session()
        sync_service = ExcelSyncService(session, Config.EXCEL_FILE_PATH)
        result = sync_service.sync_all()
        
        if result:
            return jsonify({'message': 'Excel数据同步成功'}), 200
        else:
            return jsonify({'error': 'Excel数据同步失败'}), 500
    except Exception as e:
        logging.error(f"手动同步过程中出错: {str(e)}")
        return jsonify({'error': f'同步过程中出错: {str(e)}'}), 500
```

### 3.3 数据验证机制

为确保同步数据的准确性，添加验证机制：

```python
def validate_sync_results(self):
    """验证同步结果是否正确"""
    # 从Excel和数据库分别获取数据
    wb = load_workbook_with_error_handling(self.excel_file_path, read_only=True, data_only=True)
    if wb is None:
        return False
        
    ws_orders = get_worksheet(wb, "订单管理")
    if ws_orders is None:
        return False
        
    # 验证订单数量
    excel_order_count = 0
    for i in range(2, ws_orders.max_row + 1):
        if ws_orders.cell(row=i, column=2).value:  # 有订单编号的行
            excel_order_count += 1
            
    db_order_count = self.db_session.query(Order).count()
    
    if excel_order_count != db_order_count:
        self.logger.error(f"验证失败：Excel订单数量({excel_order_count})与数据库订单数量({db_order_count})不匹配")
        return False
        
    # 验证特定关键订单的数据
    # ...
    
    return True
```

## 4. API层改造

### 4.1 改造策略

采用渐进式改造策略，逐步从Excel切换到数据库：

1. 保留原有Excel访问逻辑
2. 添加数据库访问逻辑
3. 通过配置决定使用哪种数据源
4. 验证无误后完全切换到数据库

### 4.2 API适配器模式

设计API适配器模式，使代码能够同时支持Excel和数据库两种数据源：

```python
# 适配器接口
class DataSourceAdapter:
    def get_orders_by_customer_name(self, customer_name):
        pass
        
    def get_order_details(self, order_number):
        pass
        
    # 其他数据访问方法...

# Excel数据源适配器
class ExcelDataAdapter(DataSourceAdapter):
    def __init__(self, excel_file_path):
        self.excel_file_path = excel_file_path
        
    def get_orders_by_customer_name(self, customer_name):
        # 原有的Excel处理逻辑
        wb = load_workbook_with_error_handling(self.excel_file_path, read_only=False, data_only=True)
        # ...
        
# 数据库数据源适配器
class DatabaseDataAdapter(DataSourceAdapter):
    def __init__(self, db_session):
        self.db_session = db_session
        
    def get_orders_by_customer_name(self, customer_name):
        # 新的数据库查询逻辑
        orders = self.db_session.query(Order).filter(Order.customer_name.like(f"%{customer_name}%")).all()
        # ...
        
# 工厂方法
def get_data_adapter():
    if Config.USE_DATABASE:
        return DatabaseDataAdapter(get_db_session())
    else:
        return ExcelDataAdapter(Config.EXCEL_FILE_PATH)
```

### 4.3 示例API改造

以订单查询API为例，展示改造过程：

```python
@bp.route('/filter_orders_by_customer_name', methods=['GET'])
@require_api_key('filter_orders_by_customer_name')
def filter_orders_by_customer_name():
    """根据客户姓名筛选订单，并返回订单信息"""
    customer_name_query = request.args.get('customer_name')
    if not customer_name_query:
        logging.warning("未提供客户姓名参数。")
        return jsonify({'error': '请提供客户姓名参数，如 ?customer_name=张三'}), 400

    try:
        # 获取适当的数据适配器
        adapter = get_data_adapter()
        
        # 使用适配器获取数据
        results = adapter.get_orders_by_customer_name(customer_name_query)
        
        if not results:
            logging.info(f"未找到匹配的订单，客户姓名: {customer_name_query}")
            return jsonify({'message': '未找到匹配的订单。', 'results': []}), 200

        logging.info(f"成功筛选订单，客户姓名: {customer_name_query}")
        return jsonify({'results': results})

    except Exception as e:
        logging.error(f'数据处理错误：{str(e)}')
        return jsonify({'error': '数据处理错误，请联系管理员。'}), 500
```

## 5. 实施阶段

### 5.1 准备阶段

1. **环境准备**
   - 安装所需依赖库：SQLAlchemy, APScheduler
   - 配置数据库连接参数
   - 创建数据库模型

2. **数据库初始化**
   - 编写数据库初始化脚本
   - 创建表结构和索引
   - 添加初始化命令到Flask命令行

```python
# 添加到app/__init__.py
import click
from flask.cli import with_appcontext

@click.command('init-db')
@with_appcontext
def init_db_command():
    """初始化数据库命令"""
    from app.database import init_db
    init_db()
    click.echo('数据库初始化完成。')

def create_app():
    # ...
    app.cli.add_command(init_db_command)
    # ...
```

3. **同步服务开发**
   - 实现Excel到数据库的同步逻辑
   - 编写数据验证机制
   - 创建定时任务调度器

### 5.2 过渡阶段

1. **适配器开发**
   - 实现Excel数据适配器
   - 实现数据库数据适配器
   - 创建适配器工厂

2. **API改造**
   - 保留原Excel处理逻辑
   - 添加数据库处理逻辑
   - 通过配置切换数据源

3. **测试验证**
   - 手动触发同步并验证数据
   - 对比Excel和数据库查询结果
   - 确保功能一致性

### 5.3 完全迁移阶段

1. **全面切换**
   - 将所有API切换至数据库数据源
   - 移除Excel直接读取逻辑，仅保留同步功能

2. **性能优化**
   - 优化数据库查询
   - 实现增量同步策略
   - 添加缓存机制

3. **监控系统**
   - 添加同步状态监控
   - 设置数据一致性校验
   - 添加报警机制

## 6. 部署与维护

### 6.1 部署步骤

1. **安装依赖**
```bash
pip install sqlalchemy apscheduler
```

2. **更新配置文件**
```
# .env文件
DATABASE_URI=postgresql://username:password@localhost/dbname
USE_DATABASE=False  # 初始阶段不启用数据库
SYNC_INTERVAL=3600  # 同步间隔，单位秒
```

3. **初始化数据库**
```bash
flask init-db
```

4. **首次同步**
```bash
curl -X POST "http://your-api-url/sync_excel?api_key=your-api-key"
```

5. **验证同步结果**
```bash
flask verify-sync
```

6. **启用数据库**
```
# 验证通过后，更新.env
USE_DATABASE=True
```

### 6.2 监控与报警

1. **同步状态监控**
   - 记录每次同步状态和时间
   - 提供同步历史查询API

```python
@bp.route('/sync_status', methods=['GET'])
@require_api_key('sync_status')
def get_sync_status():
    """获取同步状态信息"""
    try:
        session = get_db_session()
        status = session.query(SyncStatus).order_by(SyncStatus.sync_time.desc()).first()
        
        if status:
            return jsonify({
                'last_sync_time': status.sync_time.isoformat(),
                'status': status.status,
                'message': status.message,
                'records_synced': status.records_synced
            })
        else:
            return jsonify({
                'message': '尚未执行同步'
            })
            
    except Exception as e:
        logging.error(f"获取同步状态出错: {str(e)}")
        return jsonify({'error': f'获取同步状态出错: {str(e)}'}), 500
```

2. **同步失败报警**
   - 邮件通知
   - 短信通知
   - 接入企业通讯工具

### 6.3 备份策略

1. **数据库备份**
   - 每日自动备份
   - 增量备份策略
   - 备份文件保留策略

2. **Excel备份**
   - 同步前自动备份Excel
   - 保留历史版本

## 7. 性能优化

### 7.1 数据库优化

1. **合理的索引**
   - 为频繁查询的字段创建索引
   - 避免过多索引导致写入性能下降

2. **分区表策略**
   - 按日期分区大表
   - 提高查询速度

3. **连接池优化**
   - 配置适当的连接池大小
   - 监控连接池状态

```python
# 配置SQLAlchemy连接池
engine = create_engine(
    Config.DATABASE_URI,
    pool_size=10,
    max_overflow=20,
    pool_timeout=30,
    pool_recycle=3600
)
```

### 7.2 查询优化

1. **延迟加载与立即加载**
   - 根据需求选择合适的加载策略
   - 避免N+1查询问题

```python
# 使用join立即加载关联数据
orders = db_session.query(Order).\
    options(joinedload(Order.payment_schedules), joinedload(Order.customer_info)).\
    filter(Order.customer_name.like(f"%{customer_name}%")).\
    all()
```

2. **部分查询**
   - 只查询需要的字段
   - 减少数据传输

```python
# 只选择需要的字段
orders = db_session.query(
    Order.id,
    Order.order_number,
    Order.customer_name,
    Order.total_receivable
).\
    filter(Order.order_date >= start_date).\
    all()
```

3. **缓存机制**
   - 缓存频繁查询结果
   - 设置合理的缓存失效策略

### 7.3 增量同步策略

实现增量同步策略，减少同步开销：

```python
def incremental_sync(self):
    """增量同步策略"""
    try:
        # 获取最后同步时间
        last_sync = self.db_session.query(SyncStatus).order_by(SyncStatus.sync_time.desc()).first()
        last_sync_time = last_sync.sync_time if last_sync else None
        
        # 从Excel加载数据
        wb = load_workbook_with_error_handling(self.excel_file_path, read_only=False, data_only=True)
        if wb is None:
            return False
            
        # 同步"订单管理"表
        ws_orders = get_worksheet(wb, "订单管理")
        if ws_orders:
            for i in range(2, ws_orders.max_row + 1):
                order_date_cell = ws_orders.cell(row=i, column=1).value
                order_date = parse_date(order_date_cell)
                
                # 如果有最后同步时间，且订单日期早于最后同步时间，则跳过
                if last_sync_time and order_date and order_date < last_sync_time.date():
                    continue
                    
                # 处理该行数据...
                
        # 同步其他表...
        
        # 更新同步状态
        self._update_sync_status(True, "增量同步成功")
        return True
        
    except Exception as e:
        self._update_sync_status(False, f"增量同步失败: {str(e)}")
        return False
```

## 8. 附录

### 8.1 技术栈选择

**数据库选项**：
1. **PostgreSQL**
   - 优点：强大的开源关系型数据库，支持复杂查询和事务
   - 缺点：配置复杂些，资源占用较多

2. **MySQL/MariaDB**
   - 优点：广泛使用的开源数据库，部署简单
   - 缺点：某些高级特性不如PostgreSQL

3. **SQLite**
   - 优点：嵌入式数据库，无需单独服务，部署简单
   - 缺点：并发性能有限，不适合高并发场景

**ORM框架**：
1. **SQLAlchemy**
   - 优点：功能丰富，灵活性高，支持多种数据库
   - 缺点：学习曲线陡峭

2. **Flask-SQLAlchemy**
   - 优点：为Flask量身定制，API简洁
   - 缺点：定制化能力不如原生SQLAlchemy

**定时任务框架**：
1. **APScheduler**
   - 优点：功能丰富，支持多种调度方式
   - 缺点：配置复杂

2. **Celery**
   - 优点：分布式任务队列，支持复杂调度
   - 缺点：需要额外的消息中间件，架构复杂

### 8.2 常见问题与解决方案

1. **同步失败问题**
   - 症状：Excel同步到数据库失败
   - 解决方案：检查Excel文件格式，增加错误处理和重试机制

2. **数据不一致问题**
   - 症状：Excel和数据库数据不一致
   - 解决方案：实现数据验证机制，定期校验并自动修复

3. **性能问题**
   - 症状：数据库查询响应慢
   - 解决方案：优化索引，添加缓存，优化查询语句

4. **并发问题**
   - 症状：同时有多个请求可能导致数据同步冲突
   - 解决方案：添加同步锁机制，确保同一时间只有一个同步任务在执行