# 项目迭代优化行动指南：迈向一线大厂标准

本文档是为 Flask API 项目（数据库版）量身定制的迭代优化路线图，旨在通过一系列结构化、分阶段的任务，将项目提升至一线互联网公司的技术水准和工程规范。

**核心目标：** 专注于数据库版本，在**性能、并发、稳定性、可维护性**四个维度上进行全面升级。

---

## **核心原则**

我们将遵循以下原则指导所有开发工作：

1.  **数据驱动 (Data-Centric):** 彻底抛弃实时文件操作，所有业务逻辑必须基于可靠的数据库。
2.  **测试先行 (Test-Driven):** 建立全面的自动化测试体系，为代码质量和迭代安全提供保障。
3.  **服务化 (Service-Oriented):** 推动应用模块化，使其易于维护、扩展和监控。
4.  **可观测性 (Observability):** 确保系统内部的运行状况透明、可追踪，能被快速定位和诊断。

---

## **第一阶段：奠定基石 (高优先级，立即执行)**

此阶段旨在解决当前最致命的短板，为后续所有优化工作打下坚实的基础。

### **✅ 任务 1.1: 替换核心数据库 (SQLite → PostgreSQL)**

*   **目标：** 消除最大的并发性能瓶颈。
*   **行动步骤：**
    1.  在 `requirements.txt` 中添加 `psycopg2-binary`。
    2.  在 `.env.example` 和本地 `.env` 文件中，更新 `DATABASE_URI` 为 PostgreSQL 连接字符串。
    3.  编写一个一次性的数据迁移脚本 (`migrate_sqlite_to_pg.py`)，将 `data.db` 中的所有数据完整迁移到新的 PostgreSQL 数据库。
    4.  在本地环境中，将应用连接至 PostgreSQL 并进行完整的功能回归测试。
*   **完成标准：** 所有数据库版 API 功能正常，数据与原 SQLite 一致。

### **✅ 任务 1.2: 建立自动化测试体系 (Pytest)**

*   **目标：** 建立安全网，实现从 0 到 1 的突破。
*   **行动步骤：**
    1.  在 `requirements.txt` 中添加 `pytest` 和 `pytest-flask`。
    2.  在项目根目录创建 `tests/` 目录。
    3.  创建 `tests/conftest.py`，配置一个返回 `app` 和 `client` 的 fixture，用于测试。
    4.  **编写第一批测试用例：**
        *   `tests/test_health.py`: 测试 `/health` 端点。
        *   `tests/test_api_smoke.py`: 对 5-10 个核心只读 API 进行冒烟测试，断言 `response.status_code == 200`。
        *   `tests/test_queries.py`: 对 `OrderQueries` 中的一个核心查询方法编写单元测试。
*   **完成标准：** `pytest` 命令可以成功执行，并至少有 10 个测试用例通过。

### **✅ 任务 1.3: 规范化数据库迁移 (Flask-Migrate)**

*   **目标：** 使用行业标准工具管理数据库结构变更。
*   **行动步骤：**
    1.  在 `requirements.txt` 中添加 `Flask-Migrate`。
    2.  在 `app/__init__.py` 中初始化 `Migrate` 实例。
    3.  执行 `flask db init` 创建迁移仓库。
    4.  执行 `flask db migrate` 和 `flask db upgrade` 生成并应用初始迁移。
    5.  **删除**旧的 `run_migrations.py` 脚本。
*   **完成标准：** 数据库结构变更可以通过 `flask db` 命令进行管理。

---

## **第二阶段：性能与并发深度优化 (中期任务)**

在核心稳定后，我们开始着手解决性能问题，让系统能真正“扛得住”压力。

### **⬜ 任务 2.1: 全面审查并优化数据库查询**

*   **目标：** 提升查询效率，减少数据库负载。
*   **行动步骤：**
    1.  **添加索引：** 检查数据表，为所有外键、以及经常用于 `WHERE`, `JOIN`, `ORDER BY` 的列（如 `due_date`, `status`, `customer_name`）添加索引。
    2.  **解决 N+1 问题：** 审查代码，使用 SQLAlchemy 的 `joinedload` 或 `selectinload` 预加载关联数据。
    3.  **利用数据库聚合：** 尽可能使用 `func.sum`, `func.count` 等，让数据库完成计算，而不是在 Python 中处理。

### **⬜ 任务 2.2: 引入 Redis 缓存**

*   **目标：** 对计算密集型或不常变化的数据进行缓存，降低响应时间。
*   **行动步骤：**
    1.  部署 Redis 服务。
    2.  在 `requirements.txt` 中添加 `Flask-Caching` 和 `redis`。
    3.  在 `config.py` 中配置 `CACHE_TYPE = 'redis'`。
    4.  对报表、汇总等**只读** API 使用 `@cache.cached(timeout=...)` 装饰器。

### **⬜ 任务 2.3: 实现 API 分页**

*   **目标：** 防止单个请求返回过多数据，导致内存和带宽问题。
*   **行动步骤：**
    1.  为所有返回列表数据的 API 添加 `page` 和 `per_page` 查询参数。
    2.  使用 Flask-SQLAlchemy 提供的 `.paginate()` 方法实现后端分页逻辑。
    3.  在 API 响应中包含分页元数据（如 `total_pages`, `current_page`, `has_next` 等）。

---

## **第三阶段：对齐一线工程规范 (长期/持续进行)**

这是将项目从“能用”提升到“可靠、可维护”的阶段。

### **⬜ 任务 3.1: 建立 CI/CD 流水线 (GitHub Actions)**

*   **目标：** 自动化测试、构建和部署流程。
*   **行动步骤：**
    1.  创建 `.github/workflows/main.yml` 文件。
    2.  **第一步 (CI):** 配置工作流，在每次 `push` 或 `pull_request` 时自动执行 `pytest`。
    3.  **第二步 (Lint):** 在 CI 中加入代码风格检查步骤（如 `black --check .`）。
    4.  **第三步 (CD):** 在代码合并到主分支后，自动构建 Docker 镜像并推送到镜像仓库（如 Docker Hub, Aliyun ACR）。

### **⬜ 任务 3.2: 结构化日志与监控**

*   **目标：** 提升系统的可观测性，便于快速排错。
*   **行动步骤：**
    1.  集成 `structlog` 库，将所有日志输出为 JSON 格式。
    2.  集成 `prometheus-flask-exporter` 库，向外暴露 `/metrics` 端点。

### **⬜ 任务 3.3: 强化安全与配置管理**

*   **目标：** 保护敏感信息，扫描已知漏洞。
*   **行动步骤：**
    1.  **立即行动：** 确保生产环境的 `.env` 文件或包含密钥的任何文件都已加入 `.gitignore`，绝不能提交到版本库。
    2.  **长期目标：** 调研并引入配置中心（如 HashiCorp Vault）或使用云厂商的密钥管理服务。
    3.  在 CI 流程中加入 `pip-audit` 步骤，扫描依赖库的安全漏洞。

---

## **执行策略与建议**

*   **聚焦重点：** 严格按照 `第一阶段 → 第二阶段 → 第三阶段` 的顺序推进。当前的首要任务是完成第一阶段的所有内容。
*   **小步快跑：** 将每个任务拆解成可以快速完成和验证的小步骤，持续集成，避免一次性进行过多改动。
*   **数据说话：** 在进行性能优化时，使用工具（如 `EXPLAIN ANALYZE`, APM工具）来量化优化前后的效果。
*   **文档同步：** 任何架构变更（如引入 Redis），都应同步更新项目的 `README.md` 和部署文档。
