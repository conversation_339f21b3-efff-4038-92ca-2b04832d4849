"""
Demo: Build a small in-memory dataset to test import report builder.
This does not require Postgres or Excel.
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import date
import importlib.util
import os


def load_etl_module():
    here = os.path.dirname(os.path.abspath(__file__))
    target = os.path.abspath(os.path.join(here, '..', 'etl.py'))
    spec = importlib.util.spec_from_file_location("etl_mod", target)
    mod = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mod)  # type: ignore
    return mod


def main():
    etl = load_etl_module()
    # Use SQLite in-memory for demo
    engine = create_engine('sqlite:///:memory:', echo=False)
    etl.Base.metadata.create_all(engine)
    Sess = sessionmaker(bind=engine)
    s = Sess()

    # Create one lease 4+2 order
    order = etl.Order(
        order_date=date(2025, 1, 1),
        order_number='TEST-4******',
        customer_name='张三',
        model='iPhone16 Pro Max 512G',
        product_type='租赁',
        periods=4,
        total_receivable= (610 + 2389.5*4 + 4202),
        devices_count=1,
        status='在途'
    )
    s.add(order)
    s.flush()

    # Payment schedules: 1-4 rent, 5 buyout/2, 6 buyout/2 - dp
    rents = [2389.5, 2389.5, 2389.5, 2389.5]
    p5 = 4202/2
    p6 = 4202/2 - 610
    due4 = date(2025, 1, 15)
    for i, r in enumerate(rents, start=1):
        s.add(etl.PaymentSchedule(order_id=order.id, period_number=i, due_date=due4 if i==4 else date(2025, 1, 15), amount=r, status='未还'))
    s.add(etl.PaymentSchedule(order_id=order.id, period_number=5, due_date=due4.replace(month=2), amount=p5, status='未还'))
    s.add(etl.PaymentSchedule(order_id=order.id, period_number=6, due_date=due4.replace(month=3), amount=p6, status='未还'))
    s.commit()

    # Transactions: 首付, 4x租金, 2x尾款（“买一/买二”模拟归属期）
    s.add(etl.Transaction(order_id=order.id, transaction_date=date(2025, 1, 5), transaction_type='首付款', amount=610, period_number='第1期'))
    for i, r in enumerate(rents, start=1):
        s.add(etl.Transaction(order_id=order.id, transaction_date=date(2025, 1, 10+i), transaction_type='租金', amount=r, period_number=f'{i}期'))
    s.add(etl.Transaction(order_id=order.id, transaction_date=date(2025, 2, 20), transaction_type='尾款', amount=p5, period_number='买一'))
    s.add(etl.Transaction(order_id=order.id, transaction_date=date(2025, 3, 20), transaction_type='尾款', amount=p6, period_number='M2'))
    s.commit()

    # Run status update to populate paid/delta
    etl.update_payment_status_and_receivable(s)

    # Build report
    report = etl.build_import_report(s)
    from pprint import pprint
    pprint(report['summary'])
    # Show the first order details
    pprint(report['orders'][0])


if __name__ == '__main__':
    main()

