from app import create_app
from app.utils.report_store import make_run_id, save_report


def main():
    app = create_app()
    app.config['TESTING'] = True
    client = app.test_client()

    # Prepare a dummy report
    run_id = make_run_id()
    save_report(run_id, {'summary': {'total_orders': 3, 'total_schedules': 18, 'contract_check_pass': 3, 'contract_check_fail': 0}, 'orders': [], 'issues': []})

    # Set session as logged in
    with client.session_transaction() as sess:
        sess['logged_in'] = True
        sess['username'] = 'tester'

    # Fetch by run_id
    resp = client.get(f'/api/etl/report?run_id={run_id}')
    print('GET by run_id status:', resp.status_code)
    print('Body:', resp.json)

    # Fetch latest
    resp2 = client.get('/api/etl/report?last=true')
    print('GET last status:', resp2.status_code)
    print('Body:', resp2.json)


if __name__ == '__main__':
    main()

