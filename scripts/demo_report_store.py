from app.utils.report_store import make_run_id, save_report, load_report, load_latest_report


def main():
    # Create two reports
    rid1 = make_run_id()
    save_report(rid1, {'summary': {'total_orders': 1}, 'orders': []})
    rid2 = make_run_id()
    save_report(rid2, {'summary': {'total_orders': 2}, 'orders': []})

    # Load specific
    r1 = load_report(rid1)
    r2 = load_report(rid2)
    print('Report1:', r1)
    print('Report2:', r2)

    # Load latest
    latest = load_latest_report()
    print('Latest:', latest[0], latest[1] if latest else None)


if __name__ == '__main__':
    main()

