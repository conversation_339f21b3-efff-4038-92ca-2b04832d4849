from app import create_app
from app.utils.report_store import make_run_id, save_report


def main():
    app = create_app()
    app.config['TESTING'] = True
    client = app.test_client()

    # Prepare a dummy report
    run_id = make_run_id()
    report = {
        'summary': {'total_orders': 1, 'total_schedules': 2, 'contract_check_pass': 1, 'contract_check_fail': 0},
        'orders': [{
            'order_number': 'ORD-001',
            'product_type': '租赁',
            'schedules': [
                {'period': 1, 'due_date': '2025-01-15', 'expected_amount': 100.0, 'paid_amount': 100.0, 'delta_amount': 0.0, 'status': '按时还款'},
                {'period': 2, 'due_date': '2025-02-15', 'expected_amount': 150.0, 'paid_amount': 100.0, 'delta_amount': 50.0, 'status': '逾期未还'},
            ]
        }],
        'issues': []
    }
    save_report(run_id, report)

    with client.session_transaction() as sess:
        sess['logged_in'] = True
        sess['username'] = 'tester'

    # JSON download
    rj = client.get(f'/api/etl/report/download?run_id={run_id}&format=json')
    print('download json status:', rj.status_code)
    print('json length:', len(rj.data))

    # CSV download
    rc = client.get(f'/api/etl/report/download?run_id={run_id}&format=csv')
    print('download csv status:', rc.status_code)
    print('csv content:\n', rc.data.decode('utf-8'))


if __name__ == '__main__':
    main()

