-- PostgreSQL数据库初始化脚本
-- 该脚本在PostgreSQL容器首次启动时自动执行

-- 设置数据库编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 创建扩展（如果需要）
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建必要的角色和权限
-- 注意：主用户已经在Docker环境变量中配置，这里只是示例

-- 为应用用户设置默认权限
ALTER DEFAULT PRIVILEGES GRANT ALL ON TABLES TO flask_user;
ALTER DEFAULT PRIVILEGES GRANT ALL ON SEQUENCES TO flask_user;
ALTER DEFAULT PRIVILEGES GRANT ALL ON FUNCTIONS TO flask_user;

-- 输出初始化完成信息
SELECT 'PostgreSQL database initialized successfully' AS status;